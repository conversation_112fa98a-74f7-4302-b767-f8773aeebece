package com.sinitek.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.license.dto.SearchLicProjectDTO;
import com.sinitek.license.entity.LicProject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface LicProjectMapper extends BaseMapper<LicProject> {

    /**
     * 分页搜索项目
     */
    IPage<LicProject> searchLicProjects(@Param("page") Page<LicProject> page, @Param("searchParam") SearchLicProjectDTO searchLicProjectDTO);
}
