package com.sinitek.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.license.dto.SearchLicProductDTO;
import com.sinitek.license.entity.LicProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface LicProductMapper extends BaseMapper<LicProduct> {

    /**
     * 分页搜索产品
     */
    IPage<LicProduct> searchLicProducts(Page<LicProduct> page, @Param("dto") SearchLicProductDTO searchLicProductDTO);
}
