package com.sinitek.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.license.dto.SearchLicModuleDTO;
import com.sinitek.license.entity.LicModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模块Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface LicModuleMapper extends BaseMapper<LicModule> {

    /**
     * 分页搜索模块
     */
    IPage<LicModule> searchLicModules(Page<LicModule> page, @Param("dto") SearchLicModuleDTO searchLicModuleDTO);

    /**
     * 根据产品ID查找模块列表
     */
    List<LicModule> findLicModulesByProductId(@Param("productId") Long productId);
}
