package com.sinitek.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.license.entity.LicLicenseModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * License模块关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface LicLicenseModuleMapper extends BaseMapper<LicLicenseModule> {

    /**
     * 根据License ID查找模块关联
     */
    List<LicLicenseModule> findByLicenseId(@Param("licenseId") Long licenseId);
}
