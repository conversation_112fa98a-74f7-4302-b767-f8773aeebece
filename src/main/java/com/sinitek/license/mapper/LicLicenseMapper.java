package com.sinitek.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.license.dto.SearchLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseResultDTO;
import com.sinitek.license.entity.LicLicense;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * License Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface LicLicenseMapper extends BaseMapper<LicLicense> {

    /**
     * 分页搜索License
     */
    IPage<SearchLicLicenseResultDTO> searchLicLicenses(@Param("page") Page<LicLicense> page, @Param("searchParam") SearchLicLicenseDTO searchLicLicenseDTO);
}
