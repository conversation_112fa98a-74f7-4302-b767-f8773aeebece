package com.sinitek.license.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.CopyLicProductDTO;
import com.sinitek.license.dto.LoadLicProductDTO;
import com.sinitek.license.dto.LicProductDTO;
import com.sinitek.license.dto.SaveLicProductDTO;
import com.sinitek.license.dto.SearchLicProductDTO;
import com.sinitek.license.dto.SearchLicProductResultDTO;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;

import java.util.List;

/**
 * 产品服务接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ILicProductService {

    /**
     * 保存或更新产品
     */
    void saveOrUpdateLicProduct(SaveLicProductDTO saveLicProductDTO);

    /**
     * 复制产品
     */
    void copyLicProduct(CopyLicProductDTO copyLicProductDTO);

    /**
     * 根据ID获取产品
     */
    LoadLicProductDTO getLicProduct(Long id);

    /**
     * 查找产品可选项列表
     */
    List<DataOptionDTO> findLicProductOptions();

    /**
     * 搜索产品分页
     */
    IPage<SearchLicProductResultDTO> searchLicProducts(SearchLicProductDTO searchLicProductDTO);

    /**
     * 删除产品（支持单个或批量）
     */
    void deleteLicProducts(List<Long> ids);
}
