package com.sinitek.license.service;

import com.sinitek.license.dto.LicenseSettingConfigDTO;

/**
 * License设置配置服务接口
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
public interface ILicenseSettingConfigService {

    /**
     * 加载License设置配置
     *
     * @return License设置配置DTO
     */
    LicenseSettingConfigDTO loadLicenseSettingConfig();

    /**
     * 保存License设置配置
     *
     * @param configDTO License设置配置DTO
     */
    void saveLicenseSettingConfig(LicenseSettingConfigDTO configDTO);
}
