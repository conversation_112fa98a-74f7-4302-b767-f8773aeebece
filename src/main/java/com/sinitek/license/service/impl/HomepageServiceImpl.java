package com.sinitek.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.license.dto.HomepageExpireLicenseDTO;
import com.sinitek.license.dto.HomepageStatisticsDTO;
import com.sinitek.license.entity.LicLicense;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.entity.LicProject;
import com.sinitek.license.enumerate.LicenseStatusEnum;
import com.sinitek.license.util.LicenseEnumUtil;
import com.sinitek.license.mapper.LicLicenseMapper;
import com.sinitek.license.mapper.LicModuleMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.mapper.LicProjectMapper;
import com.sinitek.license.service.IHomepageService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页服务实现
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@Service
@Setter(onMethod_ = @Autowired)
public class HomepageServiceImpl implements IHomepageService {

    private LicLicenseMapper licLicenseMapper;
    private LicProjectMapper licProjectMapper;
    private LicProductMapper licProductMapper;

    @Override
    public List<HomepageExpireLicenseDTO> getExpireLicenses(int days) {
        Date now = new Date();
        Date afterDays = Date.from(LocalDate.now().plusDays(days).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 查询即将过期的License
        LambdaQueryWrapper<LicLicense> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LicLicense::getPublishStatus, LicenseStatusEnum.ISSUED.getValue())
                .eq(LicLicense::getThreadLatestFlag, 1)
                .ge(LicLicense::getValidEndTime, now)
                .le(LicLicense::getValidEndTime, afterDays)
                .orderByAsc(LicLicense::getValidEndTime);

        List<LicLicense> licenses = licLicenseMapper.selectList(wrapper);

        if (licenses.isEmpty()) {
            return List.of();
        }

        // 批量查询项目信息
        List<Long> projectIds = licenses.stream()
                .map(LicLicense::getProjectId)
                .distinct()
                .toList();

        Map<Long, LicProject> projectMap = licProjectMapper.selectBatchIds(projectIds)
                .stream()
                .collect(Collectors.toMap(LicProject::getId, project -> project));

        // 批量查询产品信息
        List<Long> productIds = projectMap.values().stream()
                .map(LicProject::getProductId)
                .distinct()
                .toList();

        Map<Long, LicProduct> productMap = licProductMapper.selectBatchIds(productIds)
                .stream()
                .collect(Collectors.toMap(LicProduct::getId, product -> product));

        // 转换为DTO
        return licenses.stream()
                .map(license -> convertToExpireDTO(license, projectMap, productMap))
                .toList();
    }

    /**
     * 转换为即将过期License DTO
     */
    private HomepageExpireLicenseDTO convertToExpireDTO(LicLicense license,
                                                        Map<Long, LicProject> projectMap,
                                                        Map<Long, LicProduct> productMap) {
        HomepageExpireLicenseDTO dto = new HomepageExpireLicenseDTO();
        dto.setLicenseId(license.getId());
        dto.setValidEndTime(license.getValidEndTime());

        // 计算剩余天数
        LocalDate now = LocalDate.now();
        LocalDate endDate = license.getValidEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long surplusDay = ChronoUnit.DAYS.between(now, endDate);


        // 设置License类型名称
        dto.setTypeName(LicenseEnumUtil.getLicenseTypeName(license.getType()));

        // 设置项目和产品信息
        LicProject project = projectMap.get(license.getProjectId());
        if (project != null) {
            dto.setProjectName(project.getName());
            dto.setCustomerName(project.getCustomerName());

            LicProduct product = productMap.get(project.getProductId());
            if (product != null) {
                dto.setProductLabel(product.getName() + "-" + product.getProductVersion());
            }
        }

        return dto;
    }
}
