package com.sinitek.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.data.model.version.service.impl.BaseVersionServiceImpl;
import com.sinitek.license.constant.LicenseConstant;
import com.sinitek.license.dto.LicenseFileDTO;
import com.sinitek.license.dto.LicenseDataDTO;
import com.sinitek.license.dto.LoadLicLicenseDTO;
import com.sinitek.license.dto.SaveLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseResultDTO;
import com.sinitek.license.dto.LicenseModuleDTO;
import com.sinitek.license.entity.LicLicense;
import com.sinitek.license.entity.LicLicenseModule;
import com.sinitek.license.entity.LicProject;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.entity.LicModule;
import com.sinitek.license.enumerate.LicenseStatusEnum;
import com.sinitek.license.enumerate.LicenseTypeEnum;
import com.sinitek.license.mapper.LicLicenseMapper;
import com.sinitek.license.mapper.LicLicenseModuleMapper;
import com.sinitek.license.mapper.LicProjectMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.mapper.LicModuleMapper;
import com.sinitek.license.service.ILicLicenseService;
import com.sinitek.license.support.keypair.ILicenseKeyPairService;
import com.sinitek.license.util.LicenseFileUtil;
import com.sinitek.license.util.LicenseEnumUtil;
import com.sinitek.license.util.LicLicenseUtil;
import com.sinitek.license.util.LicenseDateUtil;
import com.sinitek.license.util.LicProductUtil;
import com.sinitek.license.util.StringSplitUtil;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.utils.HttpUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * License服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@Setter(onMethod_ = @Autowired)
public class LicLicenseServiceImpl implements ILicLicenseService {

    private LicLicenseEntityService licLicenseEntityService;
    private LicLicenseModuleEntityService licLicenseModuleEntityService;
    private LicLicenseMapper licLicenseMapper;
    private LicProjectMapper licProjectMapper;
    private LicProductMapper licProductMapper;
    private LicModuleMapper licModuleMapper;
    private ILicenseKeyPairService licenseKeyPairService;
    private IAttachmentService attachmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLicLicense(SaveLicLicenseDTO saveLicLicenseDTO) {
        // 校验项目ID存在性
        validateProjectExists(saveLicLicenseDTO.getProjectId());

        // 校验项目是否已有License（仅新增时校验）
        if (saveLicLicenseDTO.getId() == null) {
            validateProjectLicenseUnique(saveLicLicenseDTO.getProjectId());
        }

        // 校验License时间逻辑
        validateLicenseTimeLogic(saveLicLicenseDTO.getValidBeginTime(),
            saveLicLicenseDTO.getValidEndTime());

        // 校验MAC地址格式和必填性
        validateMacAddress(saveLicLicenseDTO.getType(), saveLicLicenseDTO.getMacAddress());

        // 校验模块授权
        if (saveLicLicenseDTO.getModules() != null && !saveLicLicenseDTO.getModules().isEmpty()) {
            validateLicenseModules(saveLicLicenseDTO.getProjectId(), saveLicLicenseDTO.getModules(),
                saveLicLicenseDTO.getValidBeginTime(), saveLicLicenseDTO.getValidEndTime());
        }

        LicLicense licLicense = null;

        // 更新操作
        if (saveLicLicenseDTO.getId() != null) {
            LicLicense tempLicense = licLicenseEntityService.getById(saveLicLicenseDTO.getId());
            BeanUtils.copyProperties(saveLicLicenseDTO, tempLicense);
            licLicense = tempLicense;
        } else {
            licLicense = LicLicenseUtil.convertSaveToEntity(saveLicLicenseDTO);
        }

        licLicenseEntityService.saveVersion(licLicense);

        // 保存License模块关联
        if (saveLicLicenseDTO.getModules() != null && !saveLicLicenseDTO.getModules().isEmpty()) {
            saveLicenseModules(licLicense.getId(), saveLicLicenseDTO.getModules());
        }
    }

    @Override
    public LoadLicLicenseDTO loadLicLicense(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("License ID不能为空");
        }

        LicLicense licLicense = licLicenseEntityService.getById(id);
        if (licLicense == null) {
            throw new IllegalArgumentException("License不存在，License ID：" + id);
        }

        LoadLicLicenseDTO result = LicLicenseUtil.convertToLoadDTO(licLicense);

        setProjectInfo(result, licLicense.getProjectId());

        // 设置License模块信息
        setLicenseModules(result, licLicense.getId());

        // 设置剩余天数
        String surplusDay = LicenseDateUtil.calculateSurplusDay(licLicense.getType(), licLicense.getValidEndTime());
        result.setSurplusDay(surplusDay);

        return result;
    }

    @Override
    public IPage<SearchLicLicenseResultDTO> searchLicLicenses(
        SearchLicLicenseDTO searchLicLicenseDTO) {
        Page<LicLicense> page = searchLicLicenseDTO.buildPage();
        // TODO 处理最大剩余天数字段，如果最大剩余天数字段存在，则生成validEndTime


        IPage<SearchLicLicenseResultDTO> result = licLicenseMapper.searchLicLicenses(
            page, searchLicLicenseDTO);
        return result.convert(dto -> {
            String productLabel = LicProductUtil.buildProductLabel(dto.getProductName(),
                dto.getProductVersion());
            dto.setProductLabel(productLabel);

            // 计算剩余天数
            String surplusDay = LicenseDateUtil.calculateSurplusDay(dto.getType(), dto.getValidEndTime());
            dto.setSurplusDay(surplusDay);

            // 当为永久版时，不显示过期时间
            if (LicenseTypeEnum.PERMANENT.getValue().equals(dto.getType())) {
                dto.setValidEndTime(null);
            }

            return dto;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLicLicenses(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 批量查询License状态，只有未签发状态的License可以删除
        List<LicLicense> licenses = licLicenseEntityService.listByIds(ids);
        for (LicLicense license : licenses) {
            if (license != null && !LicenseStatusEnum.NOT_ISSUED.getValue()
                .equals(license.getPublishStatus())) {
                throw new IllegalArgumentException("只有未签发状态的License可以删除");
            }
        }

        // 批量删除License关联的模块数据
        licLicenseModuleEntityService.deleteByLicenseIds(ids);

        // 删除License主数据
        licLicenseEntityService.removeByIds(ids);

        log.info("成功删除{}个License及其关联数据", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishLicense(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("License ID不能为空");
        }

        LicLicense license = licLicenseEntityService.getById(id);
        if (license == null) {
            throw new IllegalArgumentException("License不存在");
        }

        if (!LicenseStatusEnum.NOT_ISSUED.getValue().equals(license.getPublishStatus())) {
            throw new IllegalArgumentException("只有未签发状态的License才可以签发");
        }

        // 校验License信息完整性
        validateLicenseForPublish(license);

        // 生成License文件
        try {
            // 1. 生成密钥对
            Pair<String, String> keyPair = licenseKeyPairService.generateKeyPairStrings();
            license.setPublicKey(keyPair.getFirst());
            license.setPrivateKey(keyPair.getSecond());
            licLicenseEntityService.updateById(license);

            // 2. 构建License数据结构
            LicenseDataDTO licenseData = buildLicenseData(license);

            // 3. 生成License二进制文件
            LicenseFileDTO binaryFileDTO = licenseKeyPairService.generateLicenseBinaryFile(
                licenseData, license.getPrivateKey(), license.getPublicKey());

            // 4. 生成文件并保存为附件
            File file = LicenseFileUtil.generateLicenseTempFile(binaryFileDTO,
                generateLicenseFileName(license));

            // 5. 保存为附件
            AttachmentUtils.saveAttachment(file, id,
                new LicLicense().getEntityNameValue(), LicenseConstant.ATTACHMENT_TYPE);

            log.info("License文件生成成功，License ID：{}，文件名：{}", id, file.getName());
        } catch (Exception e) {
            log.error("License文件生成失败，License ID：{}", id, e);
            throw new RuntimeException("License文件生成失败：" + e.getMessage(), e);
        }

        // 更新状态为已签发
        licLicenseEntityService.publish(id);

        log.info("License签发成功，License ID：{}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void republishLicense(Long id, SaveLicLicenseDTO renewLicenseDTO) {
        if (id == null) {
            throw new IllegalArgumentException("License ID不能为空");
        }

        // 获取原License
        LicLicense originalLicense = licLicenseEntityService.getById(id);
        if (originalLicense == null) {
            throw new IllegalArgumentException("License不存在");
        }

        if (!LicenseStatusEnum.ISSUED.getValue().equals(originalLicense.getPublishStatus())) {
            throw new IllegalArgumentException("只有已签发状态的License可以续签");
        }

        // 校验续签数据
        validateRenewLicenseData(renewLicenseDTO, originalLicense);

        // 校验MAC地址格式和必填性
        validateMacAddress(renewLicenseDTO.getType(), renewLicenseDTO.getMacAddress());

        // 生成新版本
        LicLicense licLicense = licLicenseEntityService.updateVersion(originalLicense.getId());
        // 设置新的id
        renewLicenseDTO.setId(licLicense.getId());

        // 终止原License
        terminateLicense(originalLicense.getId());

        // 修改新版本的数据
        BeanUtils.copyProperties(renewLicenseDTO, licLicense);

        // 保存License模块关联
        if (renewLicenseDTO.getModules() != null && !renewLicenseDTO.getModules().isEmpty()) {
            saveLicenseModules(licLicense.getId(), renewLicenseDTO.getModules());
        }

        // 先更新在签发
        licLicenseEntityService.updateById(licLicense);
        // 自动签发新License
        publishLicense(licLicense.getId());

        log.info("License续签成功，原License ID：{}，新License ID：{}", id, licLicense.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateLicense(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("License ID不能为空");
        }

        LicLicense license = licLicenseEntityService.getById(id);
        if (license == null) {
            throw new IllegalArgumentException("License不存在");
        }

        if (!LicenseStatusEnum.ISSUED.getValue().equals(license.getPublishStatus())) {
            throw new IllegalArgumentException("只有已签发状态的License可以终止");
        }

        // 更新状态为已终止
        license.setPublishStatus(LicenseStatusEnum.TERMINATED.getValue());
        licLicenseEntityService.updateById(license);
    }

    @Override
    public void downloadLicenseFile(Long id, HttpServletResponse response) {
        Attachment attachment = attachmentService.getAttachment(
            new LicLicense().getEntityNameValue(), id, LicenseConstant.ATTACHMENT_TYPE);
        HttpUtils.download(response, attachment.getName() + "." + attachment.getFileType(),
            attachment.getContent());
    }

    /**
     * 校验License签发前的信息完整性
     */
    private void validateLicenseForPublish(LicLicense license) {
        if (license.getProjectId() == null) {
            throw new IllegalArgumentException("License必须关联项目");
        }

        if (license.getValidBeginTime() == null || license.getValidEndTime() == null) {
            throw new IllegalArgumentException("License必须设置有效期");
        }

        if (license.getValidBeginTime().after(license.getValidEndTime())) {
            throw new IllegalArgumentException("License开始时间不能晚于结束时间");
        }

    }

    /**
     * 校验项目ID存在性
     */
    private void validateProjectExists(Long projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        LicProject project = licProjectMapper.selectById(projectId);
        if (project == null) {
            throw new IllegalArgumentException("项目不存在，项目ID：" + projectId);
        }
    }

    /**
     * 校验项目是否已有License
     */
    private void validateProjectLicenseUnique(Long projectId) {
        if (projectId == null) {
            return;
        }

        LambdaQueryWrapper<LicLicense> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicLicense::getProjectId, projectId);

        long count = licLicenseEntityService.count(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("该项目已存在License，一个项目只能创建一个License");
        }
    }

    /**
     * 校验MAC地址格式和必填性
     */
    private void validateMacAddress(Integer licenseType, String macAddress) {
        // 当类型不是试用版时，MAC地址为必填项
        if (licenseType != null && !licenseType.equals(LicenseTypeEnum.TRIAL.getValue())) {
            if (!StringUtils.hasText(macAddress)) {
                throw new IllegalArgumentException("非试用版License的MAC地址不能为空");
            }
        }

        // 如果MAC地址不为空，则校验格式
        if (!StringUtils.hasText(macAddress)) {
            return;
        }

        // MAC地址正则表达式：支持 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式
        String macRegex = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";

        // 使用工具类分割MAC地址
        String[] macAddresses = StringSplitUtil.splitByComma(macAddress);

        for (String mac : macAddresses) {
            if (StringUtils.hasText(mac)) {
                if (!mac.matches(macRegex)) {
                    throw new IllegalArgumentException("MAC地址格式不正确：" + mac + "，正确格式为：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX");
                }
            } else {
                throw new IllegalArgumentException("MAC地址不能为空");
            }
        }
    }

    /**
     * 校验License时间逻辑
     */
    private void validateLicenseTimeLogic(Date validBeginTime, Date validEndTime) {
        if (validBeginTime == null || validEndTime == null) {
            return;
        }

        if (validBeginTime.after(validEndTime)) {
            throw new IllegalArgumentException("License开始时间不能晚于结束时间");
        }
    }

    /**
     * 校验License模块授权
     */
    private void validateLicenseModules(Long projectId, List<LicenseModuleDTO> modules,
        Date licenseBeginTime, Date licenseEndTime) {
        // 获取项目关联的产品
        LicProject project = licProjectMapper.selectById(projectId);
        if (project == null) {
            return;
        }

        // 批量查询模块信息
        List<Long> moduleIds = modules.stream()
            .map(LicenseModuleDTO::getModuleId)
            .toList();

        Map<Long, LicModule> moduleMap = licModuleMapper.selectBatchIds(moduleIds)
            .stream()
            .collect(Collectors.toMap(LicModule::getId, module -> module));

        for (LicenseModuleDTO moduleDTO : modules) {
            // 校验模块存在性
            LicModule module = moduleMap.get(moduleDTO.getModuleId());
            if (module == null) {
                throw new IllegalArgumentException("模块不存在，模块ID：" + moduleDTO.getModuleId());
            }

            // 校验模块属于项目关联的产品
            if (!project.getProductId().equals(module.getProductId())) {
                throw new IllegalArgumentException("模块不属于项目关联的产品");
            }

            // 校验模块授权时间
            if (moduleDTO.getValidBeginTime() != null && moduleDTO.getValidEndTime() != null) {
                if (moduleDTO.getValidBeginTime().after(moduleDTO.getValidEndTime())) {
                    throw new IllegalArgumentException("模块授权开始时间不能晚于结束时间");
                }

                // 模块授权时间不能超出License时间范围
                if (licenseBeginTime != null && moduleDTO.getValidBeginTime()
                    .before(licenseBeginTime)) {
                    throw new IllegalArgumentException("模块授权开始时间不能早于License开始时间");
                }

                if (licenseEndTime != null && moduleDTO.getValidEndTime().after(licenseEndTime)) {
                    throw new IllegalArgumentException("模块授权结束时间不能晚于License结束时间");
                }
            }
        }
    }

    /**
     * 保存License模块关联
     */
    private void saveLicenseModules(Long licenseId, List<LicenseModuleDTO> modules) {
        // 先删除原有关联
        LambdaQueryWrapper<LicLicenseModule> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(LicLicenseModule::getLicenseId, licenseId);
        licLicenseModuleEntityService.remove(deleteWrapper);

        // 保存新的关联
        for (LicenseModuleDTO moduleDTO : modules) {
            LicLicenseModule licenseModule = new LicLicenseModule();
            licenseModule.setLicenseId(licenseId);
            licenseModule.setModuleId(moduleDTO.getModuleId());
            licenseModule.setValidBeginTime(moduleDTO.getValidBeginTime());
            licenseModule.setValidEndTime(moduleDTO.getValidEndTime());
            licLicenseModuleEntityService.save(licenseModule);
        }
    }

    /**
     * 设置项目信息
     */
    private void setProjectInfo(LoadLicLicenseDTO result, Long projectId) {
        if (projectId == null) {
            return;
        }

        LicProject project = licProjectMapper.selectById(projectId);
        if (project != null) {
            result.setProjectName(project.getName());
            result.setCustomerName(project.getCustomerName());

            // 设置产品标签
            if (project.getProductId() != null) {
                LicProduct product = licProductMapper.selectById(project.getProductId());
                if (product != null) {
                    result.setProductLabel(LicProductUtil.buildProductLabel(product));
                    result.setProductId(product.getId());
                }
            }
        }
    }

    /**
     * 设置License模块信息
     */
    private void setLicenseModules(LoadLicLicenseDTO result, Long licenseId) {
        List<LicLicenseModule> licenseModules = licLicenseModuleEntityService.findByLicenseId(
            licenseId);

        if (licenseModules.isEmpty()) {
            result.setModules(List.of());
            return;
        }

        // 批量查询模块信息
        List<Long> moduleIds = licenseModules.stream()
            .map(LicLicenseModule::getModuleId)
            .toList();

        Map<Long, LicModule> moduleMap = licModuleMapper.selectBatchIds(moduleIds)
            .stream()
            .collect(Collectors.toMap(LicModule::getId, module -> module));

        List<LicenseModuleDTO> moduleDTOs = licenseModules.stream()
            .map(licenseModule -> {
                LicenseModuleDTO dto = new LicenseModuleDTO();
                dto.setModuleId(licenseModule.getModuleId());
                dto.setValidBeginTime(licenseModule.getValidBeginTime());
                dto.setValidEndTime(licenseModule.getValidEndTime());

                LicModule module = moduleMap.get(licenseModule.getModuleId());
                if (module != null) {
                    dto.setModuleName(module.getName());
                    dto.setModuleCode(module.getCode());
                }

                return dto;
            })
            .toList();

        result.setModules(moduleDTOs);
    }

    /**
     * 校验续签License数据
     */
    private void validateRenewLicenseData(SaveLicLicenseDTO renewLicenseDTO, LicLicense originalLicense) {
        // 校验项目ID一致性
        if (!originalLicense.getProjectId().equals(renewLicenseDTO.getProjectId())) {
            throw new IllegalArgumentException("续签License的项目不能更改");
        }

        // 校验时间逻辑
        validateLicenseTimeLogic(renewLicenseDTO.getValidBeginTime(), renewLicenseDTO.getValidEndTime());

        // 校验模块授权
        if (renewLicenseDTO.getModules() != null && !renewLicenseDTO.getModules().isEmpty()) {
            validateLicenseModules(renewLicenseDTO.getProjectId(), renewLicenseDTO.getModules(),
                renewLicenseDTO.getValidBeginTime(), renewLicenseDTO.getValidEndTime());
        }
    }

    /**
     * 构建License数据结构
     */
    private LicenseDataDTO buildLicenseData(LicLicense license) {
        LicenseDataDTO licenseData = new LicenseDataDTO();

        // 设置基本信息
        licenseData.setType(LicenseEnumUtil.getLicenseTypeName(license.getType()));
        licenseData.setValidBeginTime(license.getValidBeginTime());
        licenseData.setValidEndTime(license.getValidEndTime());

        // 设置客户名称
        LicProject project = licProjectMapper.selectById(license.getProjectId());
        if (project != null) {
            licenseData.setCustomerName(project.getCustomerName());
        }

        // 设置MAC地址数组
        List<String> macAddresses = parseMacAddresses(license.getMacAddress());
        licenseData.setMacAddress(macAddresses);

        // 设置授权模块
        List<LicenseDataDTO.LicenseModuleDataDTO> modules = buildLicenseModules(license.getId());
        licenseData.setModules(modules);

        return licenseData;
    }

    /**
     * 解析MAC地址字符串为数组
     */
    private List<String> parseMacAddresses(String macAddress) {
        if (macAddress == null || macAddress.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.stream(StringSplitUtil.splitByComma(macAddress))
                .filter(mac -> !mac.isEmpty())
                .toList();
    }

    /**
     * 构建License模块数据
     */
    private List<LicenseDataDTO.LicenseModuleDataDTO> buildLicenseModules(Long licenseId) {
        // 查询License关联的模块
        List<LicLicenseModule> licenseModules = licLicenseModuleEntityService.findByLicenseId(licenseId);

        if (licenseModules.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询模块信息
        List<Long> moduleIds = licenseModules.stream()
                .map(LicLicenseModule::getModuleId)
                .toList();

        Map<Long, LicModule> moduleMap = licModuleMapper.selectBatchIds(moduleIds)
                .stream()
                .collect(Collectors.toMap(LicModule::getId, module -> module));

        return licenseModules.stream()
                .map(licenseModule -> {
                    LicenseDataDTO.LicenseModuleDataDTO moduleData = new LicenseDataDTO.LicenseModuleDataDTO();

                    LicModule module = moduleMap.get(licenseModule.getModuleId());
                    if (module != null) {
                        moduleData.setName(module.getName());
                        moduleData.setCode(module.getCode());
                        moduleData.setRoute(module.getRoute());
                        moduleData.setDescription(module.getDescription());

                        // 解析限制参数
                        moduleData.setLimitParam(parseLimitParam(module.getLimitParam()));
                    }

                    moduleData.setValidBeginTime(licenseModule.getValidBeginTime());
                    moduleData.setValidEndTime(licenseModule.getValidEndTime());

                    return moduleData;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析限制参数字符串
     */
    private Map<String, Object> parseLimitParam(String limitParamStr) {
        if (limitParamStr == null || limitParamStr.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(limitParamStr, Map.class);
        } catch (Exception e) {
            log.warn("解析限制参数失败，使用空Map：{}", limitParamStr, e);
            return new HashMap<>();
        }
    }

    /**
     * 生成License文件名
     * 命名规则：项目名称-类型-过期时间.lic
     */
    private String generateLicenseFileName(LicLicense license) {
        LicProject project = licProjectMapper.selectById(license.getProjectId());

        // 获取项目名称
        String projectName = project.getName();

        // 获取类型字符串
        String typeStr = LicenseEnumUtil.getLicenseTypeName(license.getType());

        // 获取过期时间字符串（格式：yyyyMMdd）
        String expireTimeStr = "permanent";
        if (license.getValidEndTime() != null) {
            expireTimeStr = DateUtil.format(license.getValidEndTime(), DatePattern.PURE_DATE_PATTERN);
        }

        // 文件名格式：项目名称-类型-过期时间.lic
        return projectName + "-" + typeStr + "-" + expireTimeStr + ".lic";
    }

    // 内部实体服务类
    @Service
    static class LicLicenseEntityService extends
        BaseVersionServiceImpl<LicLicenseMapper, LicLicense> {

    }

    @Service
    static class LicLicenseModuleEntityService extends
        ServiceImpl<LicLicenseModuleMapper, LicLicenseModule> {

        public List<LicLicenseModule> findByLicenseId(Long licenseId) {
            return this.baseMapper.findByLicenseId(licenseId);
        }

        /**
         * 批量删除License关联的模块数据
         */
        public void deleteByLicenseIds(List<Long> licenseIds) {
            if (licenseIds == null || licenseIds.isEmpty()) {
                return;
            }
            LambdaQueryWrapper<LicLicenseModule> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(LicLicenseModule::getLicenseId, licenseIds);
            this.remove(wrapper);
        }
    }

}
