package com.sinitek.license.service.impl;

import com.sinitek.license.constant.LicenseSettingConstant;
import com.sinitek.license.dto.LicenseSettingConfigDTO;
import com.sinitek.license.service.ILicenseSettingConfigService;
import com.sinitek.sirm.common.setting.service.ISettingService;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * License设置配置服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
@Slf4j
@Service
@Setter(onMethod_ = @Autowired)
public class LicenseSettingConfigServiceImpl implements ILicenseSettingConfigService {

    private ISettingService settingService;

    @Override
    public LicenseSettingConfigDTO loadLicenseSettingConfig() {

        LicenseSettingConfigDTO result = new LicenseSettingConfigDTO();

        Integer remindStrategy = SettingUtils.getIntegerValue(
            LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_STRATEGY);

        String remindTemplate = SettingUtils.getStringValue(
            LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_TEMPLATE);

        result.setRemindStrategy(remindStrategy);
        result.setRemindTemplate(remindTemplate);

        log.info("加载License设置配置");
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLicenseSettingConfig(LicenseSettingConfigDTO configDTO) {

        settingService.saveSetting(LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_STRATEGY,
            String.valueOf(configDTO.getRemindStrategy()));

        settingService.saveSetting(LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_TEMPLATE,
            configDTO.getRemindTemplate());

        log.info("保存License设置配置：{}", configDTO);
    }
}
