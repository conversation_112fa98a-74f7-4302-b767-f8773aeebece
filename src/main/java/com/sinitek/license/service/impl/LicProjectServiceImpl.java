package com.sinitek.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.license.dto.LoadLicProjectDTO;
import com.sinitek.license.dto.SaveLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectResultDTO;
import com.sinitek.license.entity.LicLicense;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.entity.LicProject;
import com.sinitek.license.mapper.LicLicenseMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.mapper.LicProjectMapper;
import com.sinitek.license.service.ILicProjectService;
import com.sinitek.license.util.LicProductUtil;
import com.sinitek.license.util.LicProjectUtil;
import com.sinitek.license.util.StringSplitUtil;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@Setter(onMethod_ = @Autowired)
public class LicProjectServiceImpl implements ILicProjectService {

    private LicProjectEntityService licProjectEntityService;
    private LicProductMapper licProductMapper;
    private LicLicenseMapper licLicenseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateLicProject(SaveLicProjectDTO saveLicProjectDTO) {
        // 校验产品ID存在性
        validateProductExists(saveLicProjectDTO.getProductId());

        // 校验项目名称唯一性
        validateProjectNameUnique(saveLicProjectDTO.getId(), saveLicProjectDTO.getName());

        // 校验消息提醒邮箱格式
        validateReceiveEmails(saveLicProjectDTO.getReceiveEmail());

        if (saveLicProjectDTO.getId() != null) {
            // 更新逻辑
            LicProject tempProject = licProjectEntityService.getById(saveLicProjectDTO.getId());
            BeanUtils.copyProperties(saveLicProjectDTO, tempProject);
            licProjectEntityService.saveOrUpdate(tempProject);
        } else {
            // 新增逻辑
            LicProject licProject = LicProjectUtil.convertSaveToEntity(saveLicProjectDTO);
            licProjectEntityService.save(licProject);
        }
    }

    @Override
    public LoadLicProjectDTO getLicProject(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        LicProject licProject = licProjectEntityService.getById(id);
        if (licProject == null) {
            throw new IllegalArgumentException("项目不存在，项目ID：" + id);
        }
        
        LoadLicProjectDTO result = LicProjectUtil.convertToLoadDTO(licProject);
        
        // 设置产品标签
        if (licProject.getProductId() != null) {
            LicProduct licProduct = licProductMapper.selectById(licProject.getProductId());
            if (licProduct != null) {
                result.setProductLabel(LicProductUtil.buildProductLabel(licProduct));
            }
        }
        
        return result;
    }

    @Override
    public IPage<SearchLicProjectResultDTO> searchLicProjects(SearchLicProjectDTO searchLicProjectDTO) {
        Page<LicProject> page = searchLicProjectDTO.buildPage();
        IPage<LicProject> licProjectPage = licProjectEntityService.searchLicProjects(page, searchLicProjectDTO);

        if (licProjectPage.getRecords().isEmpty()) {
            return new Page<>();
        }

        // 批量查询产品信息
        List<Long> productIds = licProjectPage.getRecords().stream()
                .map(LicProject::getProductId)
                .distinct()
                .toList();
        
        Map<Long, String> productLabelMap = licProductMapper.selectBatchIds(productIds)
                .stream()
                .collect(Collectors.toMap(LicProduct::getId,
                    LicProductUtil::buildProductLabel));
        
        // 使用convert方法转换分页结果
        return licProjectPage.convert(licProject -> {
            SearchLicProjectResultDTO dto = LicProjectUtil.convertToSearchResultDTO(licProject);
            if (licProject.getProductId() != null) {
                dto.setProductLabel(productLabelMap.get(licProject.getProductId()));
            }
            return dto;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLicProjects(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 校验项目是否被License使用
        validateProjectsNotUsedByLicenses(ids);

        licProjectEntityService.removeByIds(ids);
    }

    @Override
    public List<DataOptionDTO> findLicProjectOptions() {
        List<LicProject> licProjects = licProjectEntityService.list();

        return licProjects.stream()
                .map(project -> {
                    DataOptionDTO option = new DataOptionDTO();
                    option.setValue(String.valueOf(project.getId()));
                    option.setLabel(project.getName());
                    return option;
                })
                .toList();
    }

    /**
     * 校验产品ID存在性
     */
    private void validateProductExists(Long productId) {
        if (productId == null) {
            throw new IllegalArgumentException("产品ID不能为空");
        }
        
        LicProduct product = licProductMapper.selectById(productId);
        if (product == null) {
            throw new IllegalArgumentException("产品不存在，产品ID：" + productId);
        }
    }

    /**
     * 校验项目名称唯一性
     */
    private void validateProjectNameUnique(Long id, String name) {
        if (!StringUtils.hasText(name)) {
            return;
        }

        LambdaQueryWrapper<LicProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicProject::getName, name);

        // 如果是更新操作，排除自身
        if (id != null) {
            queryWrapper.ne(LicProject::getId, id);
        }

        long count = licProjectEntityService.count(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("项目名称已存在：" + name);
        }
    }

    /**
     * 校验接收提醒消息邮箱格式
     */
    private void validateReceiveEmails(String receiveEmail) {
        if (!StringUtils.hasText(receiveEmail)) {
            return;
        }

        // 邮箱正则表达式
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

        // 使用工具类分割邮箱地址
        String[] emails = StringSplitUtil.splitByComma(receiveEmail);

        for (String email : emails) {
            if (StringUtils.hasText(email)) {
                if (!email.matches(emailRegex)) {
                    throw new IllegalArgumentException("接收提醒消息邮箱格式不正确：" + email);
                }
            } else {
                // 如果分割后有空的邮箱项，说明格式有问题
                throw new IllegalArgumentException("接收提醒消息邮箱不能为空");
            }
        }
    }

    /**
     * 校验项目是否被License使用
     */
    private void validateProjectsNotUsedByLicenses(List<Long> projectIds) {
        for (Long projectId : projectIds) {
            LambdaQueryWrapper<LicLicense> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LicLicense::getProjectId, projectId);

            long count = licLicenseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new IllegalArgumentException("项目正在被License使用，无法删除");
            }
        }
    }

    // 内部实体服务类
    @Service
    static class LicProjectEntityService extends ServiceImpl<LicProjectMapper, LicProject> {
        
        public IPage<LicProject> searchLicProjects(Page<LicProject> page, SearchLicProjectDTO searchLicProjectDTO) {
            return this.baseMapper.searchLicProjects(page, searchLicProjectDTO);
        }
    }
}
