package com.sinitek.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.license.dto.LoadLicModuleDTO;
import com.sinitek.license.dto.LicModuleDTO;
import com.sinitek.license.dto.SaveLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleResultDTO;
import com.sinitek.license.entity.LicModule;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.mapper.LicModuleMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.service.ILicModuleService;
import com.sinitek.license.util.LicModuleUtil;
import com.sinitek.sirm.common.constant.CommonConstant;
import jakarta.validation.constraints.Size;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 模块服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */

@Service
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class LicModuleServiceImpl implements ILicModuleService {

    private LicModuleEntityService licModuleEntityService;
    private LicProductMapper licProductMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateLicModule(SaveLicModuleDTO saveLicModuleDTO) {
        // 校验产品ID存在性
        validateProductExists(saveLicModuleDTO.getProductId());

        // 校验模块名称在同一产品下唯一性
        validateModuleNameUnique(saveLicModuleDTO.getId(), saveLicModuleDTO.getProductId(), saveLicModuleDTO.getName());

        // 校验模块代码在同一产品下唯一性
        validateModuleCodeUnique(saveLicModuleDTO.getId(), saveLicModuleDTO.getProductId(), saveLicModuleDTO.getCode());

        // 校验模块限制参数解析是否正确
        validateLimitParam(saveLicModuleDTO.getLimitParam());

        LicModule licModule = LicModuleUtil.convertSaveToEntity(saveLicModuleDTO);
        licModuleEntityService.saveOrUpdate(licModule);
    }

    private void validateLimitParam(String limitParam) {
        // 限制参数为空，不校验
        if (!StringUtils.hasText(limitParam)) {
            return;
        }

        String[] params = limitParam.split("[,，]");
        for (String param : params) {
            param = param.trim();
            if (!StringUtils.hasText(param)) {
                continue;
            }

            String[] keyAndValue = param.split("=");
            if (keyAndValue.length != 2) {
                throw new IllegalArgumentException("限制参数格式错误，应为 key=value 格式：" + param);
            }

            String key = keyAndValue[0].trim();
            String value = keyAndValue[1].trim();

            if (!StringUtils.hasText(key)) {
                throw new IllegalArgumentException("限制参数的键不能为空：" + param);
            }

            if (!StringUtils.hasText(value)) {
                throw new IllegalArgumentException("限制参数的值不能为空：" + param);
            }

            // 校验键名格式（只允许字母、数字、下划线）
            if (!key.matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
                throw new IllegalArgumentException("限制参数的键名格式错误，只允许字母、数字、下划线，且必须以字母开头：" + key);
            }
        }
    }

    @Override
    public LoadLicModuleDTO getLicModule(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("模块ID不能为空");
        }

        LicModule licModule = licModuleEntityService.getById(id);
        if (licModule == null) {
            throw new IllegalArgumentException("模块不存在，模块ID：" + id);
        }

        return LicModuleUtil.convertToLoadDTO(licModule);
    }

    @Override
    public List<LicModuleDTO> findLicModulesByProductId(Long productId) {
        if (productId == null) {
            return List.of();
        }

        List<LicModule> licModules = licModuleEntityService.findLicModulesByProductId(productId);

        return licModules.stream()
                .map(LicModuleUtil::convertToDTO)
                .toList();
    }

    @Override
    public IPage<SearchLicModuleResultDTO> searchLicModules(SearchLicModuleDTO searchLicModuleDTO) {
        Page<LicModule> page = searchLicModuleDTO.buildPage();
        IPage<LicModule> licModulePage = licModuleEntityService.searchLicModules(page, searchLicModuleDTO);

        // 使用convert方法转换分页结果
        return licModulePage.convert(LicModuleUtil::convertToSearchResultDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLicModules(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        licModuleEntityService.removeByIds(ids);
    }



    /**
     * 校验产品ID存在性
     */
    private void validateProductExists(Long productId) {
        if (productId == null) {
            throw new IllegalArgumentException("产品ID不能为空");
        }

        LicProduct product = licProductMapper.selectById(productId);
        if (product == null) {
            throw new IllegalArgumentException("产品不存在，产品ID：" + productId);
        }
    }

    /**
     * 校验模块名称在同一产品下唯一性
     */
    private void validateModuleNameUnique(Long id, Long productId, String name) {
        if (!StringUtils.hasText(name)) {
            return;
        }

        LambdaQueryWrapper<LicModule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicModule::getProductId, productId)
                   .eq(LicModule::getName, name);

        // 如果是更新操作，排除自身
        if (id != null) {
            queryWrapper.ne(LicModule::getId, id);
        }

        long count = licModuleEntityService.count(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("模块名称在该产品下已存在：" + name);
        }
    }

    /**
     * 校验模块代码在同一产品下唯一性
     */
    private void validateModuleCodeUnique(Long id, Long productId, String code) {
        if (!StringUtils.hasText(code)) {
            return;
        }

        LambdaQueryWrapper<LicModule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicModule::getProductId, productId)
                   .eq(LicModule::getCode, code);

        // 如果是更新操作，排除自身
        if (id != null) {
            queryWrapper.ne(LicModule::getId, id);
        }

        long count = licModuleEntityService.count(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("模块代码在该产品下已存在：" + code);
        }
    }

    // 内部实体服务类
    @Service
    static class LicModuleEntityService extends ServiceImpl<LicModuleMapper, LicModule> {

        public IPage<LicModule> searchLicModules(Page<LicModule> page, SearchLicModuleDTO searchLicModuleDTO) {
            return this.baseMapper.searchLicModules(page, searchLicModuleDTO);
        }

        public List<LicModule> findLicModulesByProductId(Long productId) {
            return this.baseMapper.findLicModulesByProductId(productId);
        }
    }


}
