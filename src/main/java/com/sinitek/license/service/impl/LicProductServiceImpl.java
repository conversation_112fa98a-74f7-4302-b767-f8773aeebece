package com.sinitek.license.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.license.dto.CopyLicProductDTO;
import com.sinitek.license.dto.LoadLicProductDTO;
import com.sinitek.license.dto.LicProductDTO;
import com.sinitek.license.dto.SaveLicProductDTO;
import com.sinitek.license.dto.SearchLicProductDTO;
import com.sinitek.license.dto.SearchLicProductResultDTO;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;
import com.sinitek.license.entity.LicModule;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.entity.LicProject;
import com.sinitek.license.mapper.LicModuleMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.mapper.LicProjectMapper;
import com.sinitek.license.service.ILicProductService;
import com.sinitek.license.util.LicProductUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 产品服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */

@Service
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class LicProductServiceImpl implements ILicProductService {

    private LicProductEntityService licProductEntityService;
    private LicModuleMapper licModuleMapper;
    private LicProjectMapper licProjectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateLicProduct(SaveLicProductDTO saveLicProductDTO) {
        // 校验产品名称和版本的唯一性
        validateProductNameVersionUnique(saveLicProductDTO.getId(), saveLicProductDTO.getName(), saveLicProductDTO.getProductVersion());

        // 先获取
        if (saveLicProductDTO.getId() != null) {
            LicProduct tempProduct = licProductEntityService.getById(saveLicProductDTO.getId());
            BeanUtils.copyProperties(saveLicProductDTO, tempProduct);
            licProductEntityService.saveOrUpdate(tempProduct);
        } else {
            LicProduct licProduct = LicProductUtil.convertSaveToEntity(saveLicProductDTO);
            licProductEntityService.save(licProduct);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyLicProduct(CopyLicProductDTO copyLicProductDTO) {
        // 校验源产品存在性
        LicProduct sourceProduct = licProductEntityService.getById(copyLicProductDTO.getSourceId());
        if (sourceProduct == null) {
            throw new IllegalArgumentException("源产品不存在，产品ID：" + copyLicProductDTO.getSourceId());
        }

        // 校验新产品名称和版本的唯一性
        validateProductNameVersionUnique(null, copyLicProductDTO.getName(), copyLicProductDTO.getProductVersion());

        // 创建新产品
        LicProduct newProduct = new LicProduct();
        newProduct.setName(copyLicProductDTO.getName());
        newProduct.setProductVersion(copyLicProductDTO.getProductVersion());
        newProduct.setDescription(copyLicProductDTO.getDescription());

        // 保存新产品
        licProductEntityService.save(newProduct);

        // 复制模块信息
        copyProductModules(copyLicProductDTO.getSourceId(), newProduct.getId());
    }

    @Override
    public LoadLicProductDTO getLicProduct(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("产品ID不能为空");
        }

        LicProduct licProduct = licProductEntityService.getById(id);
        if (licProduct == null) {
            throw new IllegalArgumentException("产品不存在，产品ID：" + id);
        }

        return LicProductUtil.convertToLoadDTO(licProduct);
    }

    @Override
    public List<DataOptionDTO> findLicProductOptions() {
        List<LicProduct> licProducts = licProductEntityService.list();

        return licProducts.stream()
                .map(product -> {
                    DataOptionDTO option = new DataOptionDTO();
                    option.setValue(String.valueOf(product.getId()));
                    option.setLabel(LicProductUtil.buildProductLabel(product));
                    return option;
                })
                .toList();
    }

    @Override
    public IPage<SearchLicProductResultDTO> searchLicProducts(SearchLicProductDTO searchLicProductDTO) {
        Page<LicProduct> page = searchLicProductDTO.buildPage();
        IPage<LicProduct> licProductPage = licProductEntityService.searchLicProducts(page, searchLicProductDTO);

        // 使用convert方法转换分页结果
        return licProductPage.convert(LicProductUtil::convertToSearchResultDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLicProducts(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 校验产品是否被项目使用
        validateProductsNotUsedByProjects(ids);

        // 删除产品关联的模块
        deleteProductModules(ids);

        // 删除产品
        licProductEntityService.removeByIds(ids);
    }



    /**
     * 校验产品名称和版本的唯一性
     */
    private void validateProductNameVersionUnique(Long id, String name, String productVersion) {
        if (!StringUtils.hasText(name) || !StringUtils.hasText(productVersion)) {
            return;
        }

        LambdaQueryWrapper<LicProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicProduct::getName, name)
                   .eq(LicProduct::getProductVersion, productVersion);

        // 如果是更新操作，排除自身
        if (id != null) {
            queryWrapper.ne(LicProduct::getId, id);
        }

        long count = licProductEntityService.count(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("产品名称和版本的组合已存在：" + LicProductUtil.buildProductLabel(name, productVersion));
        }
    }

    /**
     * 校验产品是否被项目使用
     */
    private void validateProductsNotUsedByProjects(List<Long> productIds) {
        for (Long productId : productIds) {
            LambdaQueryWrapper<LicProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LicProject::getProductId, productId);

            long count = licProjectMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new IllegalArgumentException("产品正在被项目使用，无法删除");
            }
        }
    }

    /**
     * 删除产品关联的模块
     */
    private void deleteProductModules(List<Long> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return;
        }

        LambdaQueryWrapper<LicModule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(LicModule::getProductId, productIds);
        licModuleMapper.delete(queryWrapper);
    }

    /**
     * 复制产品模块
     */
    private void copyProductModules(Long sourceProductId, Long targetProductId) {
        // 查询源产品的所有模块
        List<LicModule> sourceModules = licModuleMapper.findLicModulesByProductId(sourceProductId);

        if (sourceModules == null || sourceModules.isEmpty()) {
            return;
        }

        // 复制模块到新产品
        for (LicModule sourceModule : sourceModules) {
            LicModule newModule = new LicModule();
            newModule.setName(sourceModule.getName());
            newModule.setCode(sourceModule.getCode());
            newModule.setRoute(sourceModule.getRoute());
            newModule.setLimitParam(sourceModule.getLimitParam());
            newModule.setDescription(sourceModule.getDescription());
            newModule.setProductId(targetProductId);

            // 保存新模块
            licModuleMapper.insert(newModule);
        }
    }

    // 内部实体服务类
    @Service
    static class LicProductEntityService extends ServiceImpl<LicProductMapper, LicProduct> {

        public IPage<LicProduct> searchLicProducts(Page<LicProduct> page, SearchLicProductDTO searchLicProductDTO) {
            return this.baseMapper.searchLicProducts(page, searchLicProductDTO);
        }
    }


}
