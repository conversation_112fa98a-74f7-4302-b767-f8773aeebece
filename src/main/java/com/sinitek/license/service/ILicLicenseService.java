package com.sinitek.license.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicLicenseDTO;
import com.sinitek.license.dto.SaveLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseResultDTO;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * License服务接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ILicLicenseService {

    /**
     * 保存或更新License
     */
    void saveLicLicense(SaveLicLicenseDTO saveLicLicenseDTO);

    /**
     * 根据ID获取License
     */
    LoadLicLicenseDTO loadLicLicense(Long id);

    /**
     * 搜索License分页
     */
    IPage<SearchLicLicenseResultDTO> searchLicLicenses(SearchLicLicenseDTO searchLicLicenseDTO);

    /**
     * 删除License（支持单个或批量）
     */
    void deleteLicLicenses(List<Long> ids);

    /**
     * 签发License
     */
    void publishLicense(Long id);

    /**
     * 续签License
     */
    void republishLicense(Long id, SaveLicLicenseDTO renewLicenseDTO);

    /**
     * 终止License
     */
    void terminateLicense(Long id);

    /**
     * 下载License文件
     */
    void downloadLicenseFile(Long id, HttpServletResponse response);
}
