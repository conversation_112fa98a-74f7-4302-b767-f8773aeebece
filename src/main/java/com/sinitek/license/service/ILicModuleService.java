package com.sinitek.license.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicModuleDTO;
import com.sinitek.license.dto.LicModuleDTO;
import com.sinitek.license.dto.SaveLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleResultDTO;

import java.util.List;

/**
 * 模块服务接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ILicModuleService {

    /**
     * 保存或更新模块
     */
    void saveOrUpdateLicModule(SaveLicModuleDTO saveLicModuleDTO);

    /**
     * 根据ID获取模块
     */
    LoadLicModuleDTO getLicModule(Long id);

    /**
     * 根据产品ID查找模块列表
     */
    List<LicModuleDTO> findLicModulesByProductId(Long productId);

    /**
     * 搜索模块分页
     */
    IPage<SearchLicModuleResultDTO> searchLicModules(SearchLicModuleDTO searchLicModuleDTO);

    /**
     * 删除模块（支持单个或批量）
     */
    void deleteLicModules(List<Long> ids);
}
