package com.sinitek.license.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicProjectDTO;
import com.sinitek.license.dto.SaveLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectResultDTO;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;

import java.util.List;

/**
 * 项目服务接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ILicProjectService {

    /**
     * 保存或更新项目
     */
    void saveOrUpdateLicProject(SaveLicProjectDTO saveLicProjectDTO);

    /**
     * 根据ID获取项目
     */
    LoadLicProjectDTO getLicProject(Long id);

    /**
     * 搜索项目分页
     */
    IPage<SearchLicProjectResultDTO> searchLicProjects(SearchLicProjectDTO searchLicProjectDTO);

    /**
     * 删除项目（支持单个或批量）
     */
    void deleteLicProjects(List<Long> ids);

    /**
     * 查找项目可选项列表
     */
    List<DataOptionDTO> findLicProjectOptions();
}
