package com.sinitek.license.enumerate;

import java.util.Objects;

/**
 * 提醒策略枚举
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public enum ExpireStrategyEnum {

    /**
     * 7天连续提醒
     */
    SEVEN_DAYS_CONTINUOUS(1, "7天连续提醒"),
    
    /**
     * 15天阶梯提醒
     */
    FIFTEEN_DAYS_LADDER(2, "15天阶梯提醒"),
    
    /**
     * 30天阶梯提醒
     */
    THIRTY_DAYS_LADDER(3, "30天阶梯提醒");

    private final Integer value;
    private final String name;

    ExpireStrategyEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ExpireStrategyEnum fromValue(Integer value) {
        ExpireStrategyEnum[] enumItems = ExpireStrategyEnum.values();
        for (ExpireStrategyEnum item : enumItems) {
            if (Objects.equals(item.value, value)) {
                return item;
            }
        }
        return null;
    }
}
