package com.sinitek.license.enumerate;

import java.util.Objects;

/**
 * License类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public enum LicenseTypeEnum {

    /**
     * 试用版
     */
    TRIAL(1, "试用版"),

    /**
     * 订阅版
     */
    SUBSCRIPTION(2, "订阅版"),

    /**
     * 永久版
     */
    PERMANENT(3, "永久版");

    private final Integer value;
    private final String name;

    LicenseTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static LicenseTypeEnum fromValue(Integer value) {
        LicenseTypeEnum[] enumItems = LicenseTypeEnum.values();
        for (LicenseTypeEnum item : enumItems) {
            if (Objects.equals(item.value, value)) {
                return item;
            }
        }
        return null;
    }
}
