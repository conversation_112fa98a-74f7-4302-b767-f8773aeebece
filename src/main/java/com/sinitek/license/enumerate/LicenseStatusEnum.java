package com.sinitek.license.enumerate;

import java.util.Objects;

/**
 * License状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public enum LicenseStatusEnum {

    /**
     * 未签发
     */
    NOT_ISSUED(1, "未签发"),
    
    /**
     * 已签发
     */
    ISSUED(2, "已签发"),
    
    /**
     * 已终止
     */
    TERMINATED(5, "已终止");

    private final Integer value;
    private final String name;

    LicenseStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static LicenseStatusEnum fromValue(Integer value) {
        LicenseStatusEnum[] enumItems = LicenseStatusEnum.values();
        for (LicenseStatusEnum item : enumItems) {
            if (Objects.equals(item.value, value)) {
                return item;
            }
        }
        return null;
    }
}
