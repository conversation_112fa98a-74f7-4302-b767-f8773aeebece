package com.sinitek.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.model.version.entity.BaseVersionEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * License实体类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_license")
@Schema(description = "License实体")
public class LicLicense extends BaseVersionEntity {

    @Schema(description = "License类型：1-试用版，2-订阅版，3-永久版")
    private Integer type;

    @Schema(description = "生效时间")
    private Date validBeginTime;

    @Schema(description = "到期时间")
    private Date validEndTime;

    @Schema(description = "授权设备MAC地址，多个地址用逗号分隔")
    private String macAddress;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "私钥")
    private String privateKey;

    @Schema(description = "公钥")
    private String publicKey;

    @Schema(description = "项目ID")
    private Long projectId;
}
