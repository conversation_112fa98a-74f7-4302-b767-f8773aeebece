package com.sinitek.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * License模块关联实体类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_license_module")
@Schema(description = "License模块关联实体")
public class LicLicenseModule extends BaseEntity {

    @Schema(description = "模块授权生效时间")
    private Date validBeginTime;

    @Schema(description = "模块授权到期时间")
    private Date validEndTime;

    @Schema(description = "License ID")
    private Long licenseId;

    @Schema(description = "模块ID")
    private Long moduleId;
}
