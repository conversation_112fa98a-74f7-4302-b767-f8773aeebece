package com.sinitek.license.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品实体
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_product")
@Schema(description = "产品表")
public class LicProduct extends BaseEntity {

    @Schema(description = "产品名称")
    @TableField("name")
    private String name;

    @Schema(description = "产品版本")
    @TableField("product_version")
    private String productVersion;

    @Schema(description = "产品描述")
    @TableField("description")
    private String description;
}
