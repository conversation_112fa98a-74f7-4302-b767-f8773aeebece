package com.sinitek.license.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模块实体
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_module")
@Schema(description = "模块表")
public class LicModule extends BaseEntity {

    @Schema(description = "模块名称")
    @TableField("name")
    private String name;

    @Schema(description = "模块代码")
    @TableField("code")
    private String code;

    @Schema(description = "路由")
    @TableField("route")
    private String route;

    @Schema(description = "限制参数")
    @TableField("limit_param")
    private String limitParam;

    @Schema(description = "模块描述")
    @TableField("description")
    private String description;

    @Schema(description = "产品ID")
    @TableField("product_id")
    private Long productId;
}
