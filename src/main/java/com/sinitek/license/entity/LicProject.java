package com.sinitek.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目实体类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_project")
@Schema(description = "项目实体")
public class LicProject extends BaseEntity {

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "项目经理")
    private String projectManager;

    @Schema(description = "项目经理邮箱")
    private String projectManagerEmail;

    @Schema(description = "客户经理")
    private String customerManager;

    @Schema(description = "客户经理邮箱")
    private String customerManagerEmail;

    @Schema(description = "接收提醒消息邮箱")
    private String receiveEmail;

    @Schema(description = "项目描述")
    private String description;
}
