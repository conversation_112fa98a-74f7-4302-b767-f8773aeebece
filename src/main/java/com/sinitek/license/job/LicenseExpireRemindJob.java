package com.sinitek.license.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.license.constant.LicenseConstant;
import com.sinitek.license.constant.LicenseSettingConstant;
import com.sinitek.license.entity.LicLicense;
import com.sinitek.license.entity.LicProduct;
import com.sinitek.license.entity.LicProject;
import com.sinitek.license.enumerate.LicenseStatusEnum;
import com.sinitek.license.enumerate.LicenseTypeEnum;
import com.sinitek.license.mapper.LicLicenseMapper;
import com.sinitek.license.mapper.LicProductMapper;
import com.sinitek.license.mapper.LicProjectMapper;
import com.sinitek.license.support.expire.AbstractLicenseExpireStrategy;
import com.sinitek.license.support.expire.LicenseExpireStrategyFactory;
import com.sinitek.sirm.common.constant.CommonConstant;
import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.service.IMessageTemplateExtService;
import com.sinitek.sirm.common.setting.entity.SirmSetting;
import com.sinitek.sirm.common.setting.service.ISettingService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.StatefulJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * License过期提醒定时任务
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@Component
@Setter(onMethod_ = @Autowired)
public class LicenseExpireRemindJob implements StatefulJob {

    private LicLicenseMapper licLicenseMapper;
    private LicProjectMapper licProjectMapper;
    private LicProductMapper licProductMapper;
    private LicenseExpireStrategyFactory strategyFactory;
    private IMessageTemplateExtService messageService;
    private ISettingService settingService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("License过期提醒定时任务开始执行");

        try {
            // 获取当前使用的过期策略
            AbstractLicenseExpireStrategy strategy = getExpireStrategy();

            if (strategy == null) {
                log.error("未找到有效的过期提醒策略，任务终止");
                return;
            }

            log.info("使用过期提醒策略：{}", strategy.getStrategyName());

            // 根据策略的最大提前天数构建查询条件
            Date today = new Date();
            Date maxRemindDate = Date.from(LocalDate.now()
                .plusDays(strategy.getMaxRemindDay())
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant());

            // 查询需要检查的License
            List<LicLicense> licenses = queryLicensesForRemind(today, maxRemindDate);

            if (CollUtil.isEmpty(licenses)) {
                log.info("没有即将过期的License");
                return;
            }

            log.info("查询到{}个需要检查的License", licenses.size());

            // 批量查询项目和产品信息
            Map<Long, LicProject> projectMap = getProjectMap(licenses);
            Map<Long, LicProduct> productMap = getProductMap(projectMap.values());

            // 遍历License，使用策略判断是否需要发送提醒
            for (LicLicense license : licenses) {
                if (strategy.checkRemind(license.getValidEndTime())) {
                    LicProject project = projectMap.get(license.getProjectId());
                    LicProduct product = productMap.get(project.getProductId());

                    // 发送消息提醒
                    sendRemindMessage(license, project, product);
                    log.info("发送过期提醒消息，License ID：{}，项目：{}，过期时间：{}",
                        license.getId(), project.getName(), license.getValidEndTime());
                }
            }

        } catch (Exception e) {
            log.error("License过期提醒定时任务执行失败", e);
            throw new JobExecutionException("License过期提醒定时任务执行失败", e);
        }
    }

    /**
     * 发送提醒消息
     */
    private void sendRemindMessage(LicLicense license, LicProject project, LicProduct product) {
        if (Objects.isNull(project) || Objects.isNull(product)) {
            log.warn("License关联的项目或产品不存在，无法发送消息，License ID：{}", license.getId());
            return;
        }

        MessageContextDTO messageContextDTO = new MessageContextDTO();
        String templateCode = getMessageTemplateCode();
        messageContextDTO.setCode(templateCode);

        // 构建消息参数
        Map<String, Object> params = buildMessageParams(license, project, product);
        messageContextDTO.setParams(params);

        // 构建接收人列表
        List<MessageReceiverTemplateDTO> receivers = buildReceivers(project);
        if (CollUtil.isEmpty(receivers)) {
            log.warn("没有找到有效的接收人，无法发送消息，项目：{}", project.getName());
            return;
        }
        messageContextDTO.setReceivers(receivers);

        // 发送消息
        messageService.sendMessage(messageContextDTO);
    }

    /**
     * 构建消息参数
     */
    private Map<String, Object> buildMessageParams(LicLicense license, LicProject project, LicProduct product) {
        Map<String, Object> params = new HashMap<>();

        params.put("projectName", project.getName());
        params.put("customerName", project.getCustomerName());
        params.put("projectManager", project.getProjectManager());
        params.put("customerManager", project.getCustomerManager());
        params.put("productName", product.getName());
        params.put("productVersion", product.getProductVersion());

        // License类型
        LicenseTypeEnum typeEnum = LicenseTypeEnum.fromValue(license.getType());
        params.put("licenseType", typeEnum != null ? typeEnum.getName() : "未知");

        params.put("validBeginTime", license.getPublishDate() != null ?
            DateUtil.format(license.getPublishDate(), CommonConstant.DATE_FORMAT_SHORT_STR) : "");
        params.put("validEndTime", DateUtil.format(license.getValidEndTime(), CommonConstant.DATE_FORMAT_SHORT_STR));

        // 计算剩余天数
        LocalDate now = LocalDate.now();
        LocalDate endDate = license.getValidEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long surplusDay = ChronoUnit.DAYS.between(now, endDate);
        params.put("surplusDay", surplusDay);

        return params;
    }

    /**
     * 构建接收人列表
     */
    private List<MessageReceiverTemplateDTO> buildReceivers(LicProject project) {
        List<MessageReceiverTemplateDTO> receivers = new ArrayList<>();

        // 添加项目经理
        if (StringUtils.hasText(project.getProjectManager()) && StringUtils.hasText(project.getProjectManagerEmail())) {
            MessageReceiverTemplateDTO receiver = new MessageReceiverTemplateDTO();
            receiver.setUserName(project.getProjectManager());
            receiver.setEmail(project.getProjectManagerEmail());
            receivers.add(receiver);
        }

        // 添加客户经理
        if (StringUtils.hasText(project.getCustomerManager()) && StringUtils.hasText(project.getCustomerManagerEmail())) {
            MessageReceiverTemplateDTO receiver = new MessageReceiverTemplateDTO();
            receiver.setUserName(project.getCustomerManager());
            receiver.setEmail(project.getCustomerManagerEmail());
            receivers.add(receiver);
        }

        // 添加额外的接收邮箱
        if (StringUtils.hasText(project.getReceiveEmail())) {
            String[] emails = project.getReceiveEmail().split("[,，]");
            for (String email : emails) {
                email = email.trim();
                if (StringUtils.hasText(email)) {
                    MessageReceiverTemplateDTO receiver = new MessageReceiverTemplateDTO();
                    receiver.setEmail(email);
                    receivers.add(receiver);
                }
            }
        }

        return receivers;
    }

    /**
     * 查询需要提醒的License
     */
    private List<LicLicense> queryLicensesForRemind(Date today, Date maxRemindDate) {
        LambdaQueryWrapper<LicLicense> wrapper = new LambdaQueryWrapper<>();
        // 只查找已签发的License
        wrapper.eq(LicLicense::getPublishStatus, LicenseStatusEnum.ISSUED.getValue())
            // 过期时间大于等于今天
            .ge(LicLicense::getValidEndTime, today)
            // 过期时间小于等于最大提醒日期
            .le(LicLicense::getValidEndTime, maxRemindDate)
            .orderByAsc(LicLicense::getId);

        return licLicenseMapper.selectList(wrapper);
    }

    /**
     * 获取项目信息映射
     */
    private Map<Long, LicProject> getProjectMap(List<LicLicense> licenses) {
        List<Long> projectIds = licenses.stream()
            .map(LicLicense::getProjectId)
            .distinct()
            .toList();

        return licProjectMapper.selectBatchIds(projectIds)
            .stream()
            .collect(Collectors.toMap(LicProject::getId, project -> project));
    }

    /**
     * 获取产品信息映射
     */
    private Map<Long, LicProduct> getProductMap(Collection<LicProject> projects) {
        List<Long> productIds = projects.stream()
            .map(LicProject::getProductId)
            .distinct()
            .toList();

        return licProductMapper.selectBatchIds(productIds)
            .stream()
            .collect(Collectors.toMap(LicProduct::getId, product -> product));
    }

    /**
     * 获取过期提醒策略实现类
     */
    private AbstractLicenseExpireStrategy getExpireStrategy() {
        SirmSetting setting = settingService.getSetting(
            LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_STRATEGY);
        if (Objects.isNull(setting)) {
            return null;
        }
        try {
            int strategyEnumValue = Integer.parseInt(setting.getValue());
            return strategyFactory.getStrategy(strategyEnumValue);
        } catch (NumberFormatException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取消息模板编码
     */
    private String getMessageTemplateCode() {
        SirmSetting setting = settingService.getSetting(
            LicenseSettingConstant.DEFAULT_MODULE,
            LicenseSettingConstant.REMIND_TEMPLATE);

        // 默认使用常量值
        return Objects.isNull(setting) ? LicenseConstant.DEFAULT_MSG_TEMPLATE_CODE : setting.getValue();
    }
}
