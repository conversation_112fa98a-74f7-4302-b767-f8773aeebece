package com.sinitek.license.config;


import com.sinitek.license.job.LicenseExpireRemindJob;
import com.sinitek.sirm.common.quartz.constant.QuartzConstant;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Configuration
public class LicenseJobConfig {

    public static final String JOB_NAME = "License过期提醒定时任务";

    @Bean
    public JobDetail licenseExpireRemindJobDetail() {
        return JobBuilder.newJob(LicenseExpireRemindJob.class)
            .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
            .storeDurably()
            .build();
    }

    @Bean
    public Trigger licenseExpireRemindJobTrigger(JobDetail licenseExpireRemindJobDetail) {
        return TriggerBuilder.newTrigger()
            .forJob(licenseExpireRemindJobDetail)
            .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
            .startNow()
            // 项目license到期前提醒，每天7：00执行
            .withSchedule(CronScheduleBuilder.cronSchedule("0 0 7 ? * *"))
            .usingJobData("name", JOB_NAME)
            .withDescription("按照系统公共配置中的提醒策略和提醒模板，向项目经理和客户经理发送License过期提醒邮件")
            .build();
    }

}
