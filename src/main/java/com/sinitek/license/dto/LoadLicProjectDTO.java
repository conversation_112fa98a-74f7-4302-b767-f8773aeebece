package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目加载结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "项目加载结果DTO")
public class LoadLicProjectDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private Date createTimestamp;

    @Schema(description = "更新时间")
    private Date updateTimestamp;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品标签")
    private String productLabel;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "项目经理")
    private String projectManager;

    @Schema(description = "项目经理邮箱")
    private String projectManagerEmail;

    @Schema(description = "客户经理")
    private String customerManager;

    @Schema(description = "客户经理邮箱")
    private String customerManagerEmail;

    @Schema(description = "接收提醒消息邮箱")
    private String receiveEmail;

    @Schema(description = "项目描述")
    private String description;
}
