package com.sinitek.license.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品搜索条件DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "产品搜索条件")
public class SearchLicProductDTO extends PageDataParam {

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品版本")
    private String productVersion;

    @Schema(description = "产品描述")
    private String description;
}
