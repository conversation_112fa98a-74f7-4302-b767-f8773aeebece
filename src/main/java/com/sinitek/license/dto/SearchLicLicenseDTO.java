package com.sinitek.license.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * License搜索条件DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "License搜索条件")
public class SearchLicLicenseDTO extends PageDataParam {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "License类型")
    private Integer type;

    @Schema(description = "发布状态")
    private Integer publishStatus;

    @Schema(description = "到期时间开始")
    private Date validEndTimeStart;

    @Schema(description = "到期时间结束")
    private Date validEndTimeEnd;
}
