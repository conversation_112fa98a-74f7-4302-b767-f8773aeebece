package com.sinitek.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * License加载结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "License加载结果DTO")
public class LoadLicLicenseDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "License类型")
    private Integer type;

    @Schema(description = "License类型名称")
    private String typeName;

    @Schema(description = "生效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validBeginTime;

    @Schema(description = "到期时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;

    @Schema(description = "授权设备MAC地址")
    private String macAddress;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品标签")
    private String productLabel;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "License模块列表")
    private List<LicenseModuleDTO> modules;

    @Schema(description = "剩余天数")
    private String surplusDay;

    @Schema(description = "发布状态")
    private Integer publishStatus;

    @Schema(description = "发布状态名称")
    private String publishStatusName;
}
