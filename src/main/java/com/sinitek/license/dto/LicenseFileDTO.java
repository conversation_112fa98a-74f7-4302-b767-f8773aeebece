package com.sinitek.license.dto;

import lombok.Data;

/**
 * License二进制文件DTO
 * 用于生成License文件的二进制格式
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class LicenseFileDTO {
    
    /**
     * License数据，即license结构设计定义的json格式数据
     */
    private byte[] data;
    
    /**
     * License数据的签名，使用SHA256withRSA算法
     */
    private byte[] signature;
    
    /**
     * 项目公钥，每一个license都是生成不同的密钥
     */
    private byte[] publicKey;
}
