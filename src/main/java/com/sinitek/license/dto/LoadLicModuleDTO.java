package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 模块加载结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "模块加载结果DTO")
public class LoadLicModuleDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "模块名称")
    private String name;

    @Schema(description = "模块代码")
    private String code;

    @Schema(description = "路由")
    private String route;

    @Schema(description = "限制参数")
    private String limitParam;

    @Schema(description = "模块描述")
    private String description;

    @Schema(description = "产品ID")
    private Long productId;
}
