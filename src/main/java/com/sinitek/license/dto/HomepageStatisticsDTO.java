package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 首页统计数据DTO
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Schema(description = "首页统计数据")
public class HomepageStatisticsDTO {

    @Schema(description = "总License数量")
    private Long totalLicenseCount;

    @Schema(description = "已签发License数量")
    private Long issuedLicenseCount;

    @Schema(description = "未签发License数量")
    private Long notIssuedLicenseCount;

    @Schema(description = "已终止License数量")
    private Long terminatedLicenseCount;

    @Schema(description = "7天内过期License数量")
    private Long expireIn7DaysCount;

    @Schema(description = "30天内过期License数量")
    private Long expireIn30DaysCount;

    @Schema(description = "已过期License数量")
    private Long expiredLicenseCount;

    @Schema(description = "总项目数量")
    private Long totalProjectCount;

    @Schema(description = "总产品数量")
    private Long totalProductCount;

    @Schema(description = "总模块数量")
    private Long totalModuleCount;
}
