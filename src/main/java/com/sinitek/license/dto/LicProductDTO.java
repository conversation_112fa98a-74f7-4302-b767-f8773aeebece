package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 产品返回DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "产品返回DTO")
public class LicProductDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private Date createTimestamp;

    @Schema(description = "更新时间")
    private Date updateTimestamp;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品版本")
    private String productVersion;

    @Schema(description = "产品描述")
    private String description;
}
