package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 保存产品DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "保存产品DTO")
public class SaveLicProductDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 50, message = "产品名称长度不能超过50个字符")
    private String name;

    @Schema(description = "产品版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "产品版本不能为空")
    @Size(max = 50, message = "产品版本长度不能超过50个字符")
    private String productVersion;

    @Schema(description = "产品描述")
    @Size(max = 500, message = "产品描述长度不能超过500个字符")
    private String description;
}
