package com.sinitek.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * License数据结构DTO
 * 对应license结构设计定义的json格式数据
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class LicenseDataDTO {
    
    /**
     * 类型
     */
    private String type;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validBeginTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 授权设备的mac地址数组
     */
    private List<String> macAddress;
    
    /**
     * 授权模块
     */
    private List<LicenseModuleDataDTO> modules;
    
    /**
     * License模块数据DTO
     */
    @Data
    public static class LicenseModuleDataDTO {
        
        /**
         * 名称
         */
        private String name;
        
        /**
         * 模块编码
         */
        private String code;
        
        /**
         * 模块对应的路由
         */
        private String route;
        
        /**
         * 限制参数
         */
        private Map<String, Object> limitParam;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date validBeginTime;
        
        /**
         * 过期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date validEndTime;
    }
}
