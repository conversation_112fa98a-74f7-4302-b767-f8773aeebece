package com.sinitek.license.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * 模块搜索条件DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "模块搜索条件")
public class SearchLicModuleDTO extends PageDataParam {

    @Schema(description = "模块名称")
    private String name;

    @Schema(description = "模块代码")
    private String code;

    @Schema(description = "路由")
    private String route;

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    @Schema(description = "产品名称")
    private String productName;
}
