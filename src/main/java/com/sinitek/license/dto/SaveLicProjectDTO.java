package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 保存项目DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "保存项目DTO")
public class SaveLicProjectDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 50, message = "项目名称长度不能超过50个字符")
    private String name;

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户名称不能为空")
    @Size(max = 50, message = "客户名称长度不能超过50个字符")
    private String customerName;

    @Schema(description = "项目经理", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "项目经理不能为空")
    @Size(max = 50, message = "项目经理长度不能超过50个字符")
    private String projectManager;

    @Schema(description = "项目经理邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "项目经理邮箱不能为空")
    @Email(message = "项目经理邮箱格式不正确")
    @Size(max = 100, message = "项目经理邮箱长度不能超过100个字符")
    private String projectManagerEmail;

    @Schema(description = "客户经理", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户经理不能为空")
    @Size(max = 50, message = "客户经理长度不能超过50个字符")
    private String customerManager;

    @Schema(description = "客户经理邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户经理邮箱不能为空")
    @Email(message = "客户经理邮箱格式不正确")
    @Size(max = 100, message = "客户经理邮箱长度不能超过100个字符")
    private String customerManagerEmail;

    @Schema(description = "接收提醒消息邮箱")
    @Size(max = 3000, message = "接收提醒消息邮箱长度不能超过3000个字符")
    private String receiveEmail;

    @Schema(description = "项目描述")
    @Size(max = 500, message = "项目描述长度不能超过500个字符")
    private String description;
}
