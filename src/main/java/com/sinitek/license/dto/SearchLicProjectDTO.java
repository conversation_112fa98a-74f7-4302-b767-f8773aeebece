package com.sinitek.license.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目搜索条件DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目搜索条件")
public class SearchLicProjectDTO extends PageDataParam {

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "客户名称")
    private String customerName;
}
