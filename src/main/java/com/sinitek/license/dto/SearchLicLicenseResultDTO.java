package com.sinitek.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * License搜索结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "License搜索结果DTO")
public class SearchLicLicenseResultDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "状态")
    private Integer publishStatus;

    @Schema(description = "状态名称")
    private String publishStatusName;

    @Schema(description = "License类型")
    private Integer type;

    @Schema(description = "License类型名称")
    private String typeName;

    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validBeginTime;

    @Schema(description = "过期时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "产品标签")
    private String productLabel;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "剩余天数")
    private String surplusDay;

    @JsonIgnore
    @Schema(description = "产品名称")
    private String productName;

    @JsonIgnore
    @Schema(description = "产品版本")
    private String productVersion;
}
