package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 保存模块DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "保存模块DTO")
public class SaveLicModuleDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "模块名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模块名称不能为空")
    @Size(max = 50, message = "模块名称长度不能超过50个字符")
    private String name;

    @Schema(description = "模块代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模块代码不能为空")
    @Size(max = 50, message = "模块代码长度不能超过50个字符")
    private String code;

    @Schema(description = "路由", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 50, message = "路由长度不能超过50个字符")
    private String route;

    @Schema(description = "限制参数")
    @Size(max = 500, message = "限制参数长度不能超过500个字符")
    private String limitParam;

    @Schema(description = "模块描述")
    @Size(max = 500, message = "模块描述长度不能超过500个字符")
    private String description;

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品ID不能为空")
    private Long productId;
}
