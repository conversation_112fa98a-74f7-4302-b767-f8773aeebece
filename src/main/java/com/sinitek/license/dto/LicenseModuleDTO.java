package com.sinitek.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * License模块DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "License模块DTO")
public class LicenseModuleDTO {

    @Schema(description = "模块ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模块ID不能为空")
    private Long moduleId;

    @Schema(description = "模块名称")
    private String moduleName;

    @Schema(description = "模块代码")
    private String moduleCode;

    @Schema(description = "模块授权生效时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模块授权生效时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validBeginTime;

    @Schema(description = "模块授权到期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模块授权到期时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;
}
