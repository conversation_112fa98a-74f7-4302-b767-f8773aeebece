package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 产品加载结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "产品加载结果DTO")
public class LoadLicProductDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品版本")
    private String productVersion;

    @Schema(description = "产品描述")
    private String description;
}
