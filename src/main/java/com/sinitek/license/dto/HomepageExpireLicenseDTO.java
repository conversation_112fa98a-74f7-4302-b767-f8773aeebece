package com.sinitek.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 首页即将过期License DTO
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Schema(description = "首页即将过期License")
public class HomepageExpireLicenseDTO {

    @Schema(description = "License ID")
    private Long licenseId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "产品标签")
    private String productLabel;

    @Schema(description = "License类型名称")
    private String typeName;

    @Schema(description = "过期时间")
    private Date validEndTime;

    @Schema(description = "剩余天数")
    private String surplusDay;

    @Schema(description = "剩余天数颜色标识：red-红色，orange-橙色，black-黑色")
    private String surplusDayColor;
}
