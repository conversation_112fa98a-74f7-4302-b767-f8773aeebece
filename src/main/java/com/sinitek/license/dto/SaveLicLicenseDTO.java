package com.sinitek.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 保存License DTO
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Schema(description = "保存License DTO")
public class SaveLicLicenseDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "License类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "License类型不能为空")
    private Integer type;

    @Schema(description = "生效时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生效时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validBeginTime;

    @Schema(description = "到期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "到期时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEndTime;

    @Schema(description = "授权设备MAC地址，多个地址用逗号分隔")
    @Size(max = 3000, message = "MAC地址长度不能超过3000个字符")
    private String macAddress;

    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "License模块列表")
    @Valid
    private List<LicenseModuleDTO> modules;
}
