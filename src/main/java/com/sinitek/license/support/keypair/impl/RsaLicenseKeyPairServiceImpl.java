package com.sinitek.license.support.keypair.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.license.support.keypair.ILicenseKeyPairService;
import com.sinitek.license.dto.LicenseFileDTO;
import com.sinitek.license.dto.LicenseDataDTO;
import com.sinitek.license.util.LicenseSignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * RSA算法License密钥对生成支持实现类
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@Component
public class RsaLicenseKeyPairServiceImpl implements ILicenseKeyPairService {

    private static final String ALGORITHM = "RSA";
    private static final int KEY_SIZE = 2048;

    @Override
    public Pair<String, String> generateKeyPairStrings() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
            keyPairGenerator.initialize(KEY_SIZE);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

            String publicKeyStr = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKeyStr = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());

            log.info("成功生成RSA密钥对，密钥长度：{}", KEY_SIZE);

            // 返回Pair，T1为公钥，T2为私钥
            return Pair.of(publicKeyStr, privateKeyStr);

        } catch (NoSuchAlgorithmException e) {
            log.error("生成RSA密钥对失败", e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }

    @Override
    public LicenseFileDTO generateLicenseBinaryFile(LicenseDataDTO licenseData, String privateKey, String publicKey) {
        try {
            log.info("开始生成License二进制文件");

            // 1. 将License数据转换为JSON字节数组
            ObjectMapper objectMapper = new ObjectMapper();
            byte[] dataBytes = objectMapper.writeValueAsString(licenseData).getBytes(StandardCharsets.UTF_8);

            // 2. 对数据进行签名
            byte[] signature = LicenseSignatureUtil.signData(dataBytes, privateKey);

            // 3. 获取公钥字节数组
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKey);

            // 4. 构建License二进制文件DTO
            LicenseFileDTO binaryFileDTO = new LicenseFileDTO();
            binaryFileDTO.setData(dataBytes);
            binaryFileDTO.setSignature(signature);
            binaryFileDTO.setPublicKey(publicKeyBytes);

            log.info("License二进制文件生成成功");
            return binaryFileDTO;

        } catch (Exception e) {
            log.error("生成License二进制文件失败", e);
            throw new RuntimeException("生成License二进制文件失败：" + e.getMessage(), e);
        }
    }

}
