package com.sinitek.license.support.keypair;

import com.sinitek.license.dto.LicenseFileDTO;
import com.sinitek.license.dto.LicenseDataDTO;
import org.springframework.data.util.Pair;

/**
 * License密钥对生成支持接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ILicenseKeyPairService {

    /**
     * 生成公私钥对
     *
     * @return Pair对象，T1为公钥字符串，T2为私钥字符串
     */
    Pair<String, String> generateKeyPairStrings();

    /**
     * 生成License二进制文件
     *
     * @param licenseData License数据
     * @param privateKey 私钥
     * @param publicKey 公钥
     * @return License二进制文件DTO
     */
    LicenseFileDTO generateLicenseBinaryFile(LicenseDataDTO licenseData, String privateKey, String publicKey);
}
