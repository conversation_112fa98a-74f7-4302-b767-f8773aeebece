package com.sinitek.license.support.expire;

import java.util.Date;

/**
 * License过期提醒抽象策略类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public abstract class AbstractLicenseExpireStrategy {

    /**
     * 返回枚举值，与配置的枚举值相对应
     */
    public abstract int getEnumValue();

    /**
     * 返回最大提前天数
     * 例如：license到期前30天/15天/7天/3天/1天分阶段提醒（共5次），需要返回30
     */
    public abstract int getMaxRemindDay();

    /**
     * 判断该过期时间是否需要提醒
     */
    public abstract boolean checkRemind(Date validEndTime);

    /**
     * 获取策略名称
     */
    public abstract String getStrategyName();

}
