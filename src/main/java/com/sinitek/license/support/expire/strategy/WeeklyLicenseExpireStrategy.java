package com.sinitek.license.support.expire.strategy;

import com.sinitek.license.support.expire.AbstractLicenseExpireStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 7天连续提醒策略
 * license到期前7天内每日发送提醒邮件
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Component
public class WeeklyLicenseExpireStrategy extends AbstractLicenseExpireStrategy {

    private static final int ENUM_VALUE = 1;
    private static final int MAX_REMIND_DAY = 7;

    @Override
    public int getEnumValue() {
        return ENUM_VALUE;
    }

    @Override
    public int getMaxRemindDay() {
        return MAX_REMIND_DAY;
    }

    @Override
    public boolean checkRemind(Date validEndTime) {
        if (validEndTime == null) {
            return false;
        }

        try {
            LocalDate now = LocalDate.now();
            LocalDate endDate = validEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            
            // 计算距离到期的天数
            long daysUntilExpire = ChronoUnit.DAYS.between(now, endDate);
            
            // 7天内（包括当天）需要提醒
            boolean needRemind = daysUntilExpire >= 0 && daysUntilExpire <= MAX_REMIND_DAY;
            
            if (needRemind) {
                log.info("7天连续提醒策略：License将在{}天后到期，需要发送提醒", daysUntilExpire);
            }
            
            return needRemind;
            
        } catch (Exception e) {
            log.error("7天连续提醒策略检查失败", e);
            return false;
        }
    }

    @Override
    public String getStrategyName() {
        return "7天连续提醒";
    }

}
