package com.sinitek.license.support.expire;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * License过期策略工厂类
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@Component
public class LicenseExpireStrategyFactory {

    @Autowired
    private ApplicationContext applicationContext;

    private final Map<Integer, AbstractLicenseExpireStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 通过Spring获取所有的策略实现类
        Map<String, AbstractLicenseExpireStrategy> strategies = 
            applicationContext.getBeansOfType(AbstractLicenseExpireStrategy.class);

        for (AbstractLicenseExpireStrategy strategy : strategies.values()) {
            strategyMap.put(strategy.getEnumValue(), strategy);
            log.info("注册License过期策略：{} - {}", strategy.getEnumValue(), strategy.getStrategyName());
        }
    }

    /**
     * 获取策略实现
     */
    public AbstractLicenseExpireStrategy getStrategy(int enumValue) {
        AbstractLicenseExpireStrategy strategy = strategyMap.get(enumValue);
        if (strategy == null) {
            log.warn("未找到对应的过期策略，枚举值：{}", enumValue);
            // 返回默认策略（7天连续提醒）
            return strategyMap.get(1);
        }
        return strategy;
    }
}
