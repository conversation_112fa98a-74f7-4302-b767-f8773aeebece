package com.sinitek.license.support.expire.strategy;

import com.sinitek.license.support.expire.AbstractLicenseExpireStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 15天阶梯提醒策略
 * license到期前15天/7天/3天/1天/当天分阶段提醒（共5次）
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@Component
public class FifteenDaysLadderLicenseExpireStrategy extends AbstractLicenseExpireStrategy {

    private static final int ENUM_VALUE = 2;
    private static final int MAX_REMIND_DAY = 15;
    private static final int[] REMIND_DAYS = {0, 1, 3, 7, 15};

    @Override
    public int getEnumValue() {
        return ENUM_VALUE;
    }

    @Override
    public int getMaxRemindDay() {
        return MAX_REMIND_DAY;
    }

    @Override
    public boolean checkRemind(Date validEndTime) {
        if (validEndTime == null) {
            return false;
        }

        try {
            LocalDate now = LocalDate.now();
            LocalDate endDate = validEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            
            // 计算距离到期的天数
            long daysUntilExpire = ChronoUnit.DAYS.between(now, endDate);
            
            // 检查是否在提醒天数列表中
            for (int remindDay : REMIND_DAYS) {
                if (daysUntilExpire == remindDay) {
                    log.info("15天阶梯提醒策略：License将在{}天后到期，需要发送提醒", daysUntilExpire);
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("15天阶梯提醒策略检查失败", e);
            return false;
        }
    }

    @Override
    public String getStrategyName() {
        return "15天阶梯提醒";
    }

}
