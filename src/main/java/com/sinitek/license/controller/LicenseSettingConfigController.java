package com.sinitek.license.controller;

import com.sinitek.license.dto.LicenseSettingConfigDTO;
import com.sinitek.license.service.ILicenseSettingConfigService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * License设置配置控制器
 *
 * <AUTHOR>
 * @date 2025/6/18
 */
@RestController
@RequestMapping("/frontend/api/license/setting-config")
@Tag(name = "License设置配置管理", description = "License设置配置相关接口")
@Setter(onMethod_ = @Autowired)
public class LicenseSettingConfigController {

    private ILicenseSettingConfigService licenseSettingConfigService;

    @GetMapping("/load")
    @Operation(summary = "加载License设置配置", description = "获取License设置配置信息")
    public RequestResult<LicenseSettingConfigDTO> loadLicenseSettingConfig() {
        LicenseSettingConfigDTO result = licenseSettingConfigService.loadLicenseSettingConfig();
        return new RequestResult<>(result);
    }

    @PostMapping("/save")
    @Operation(summary = "保存License设置配置", description = "保存License设置配置信息")
    public RequestResult<Void> saveLicenseSettingConfig(@RequestBody @Validated @Parameter(description = "配置信息") LicenseSettingConfigDTO configDTO) {
        licenseSettingConfigService.saveLicenseSettingConfig(configDTO);
        return new RequestResult<>();
    }
}
