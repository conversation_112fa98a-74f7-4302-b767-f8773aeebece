package com.sinitek.license.controller;

import com.sinitek.license.dto.HomepageExpireLicenseDTO;
import com.sinitek.license.dto.HomepageStatisticsDTO;
import com.sinitek.license.service.IHomepageService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 首页控制器
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/frontend/api/homepage")
@Tag(name = "首页管理", description = "首页统计和数据展示相关接口")
@Setter(onMethod_ = @Autowired)
public class HomepageController {

    private IHomepageService homepageService;

    @GetMapping("/expire-list")
    @Operation(summary = "获取指定天数内即将过期的License列表", description = "获取指定天数内即将过期的License列表")
    public RequestResult<List<HomepageExpireLicenseDTO>> getExpireLicenses(
            @RequestParam @Parameter(description = "天数") int days) {
        List<HomepageExpireLicenseDTO> expireLicenses = homepageService.getExpireLicenses(days);
        return new RequestResult<>(expireLicenses);
    }
}
