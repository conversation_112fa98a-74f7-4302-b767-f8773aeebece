package com.sinitek.license.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicModuleDTO;
import com.sinitek.license.dto.LicModuleDTO;
import com.sinitek.license.dto.SaveLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleResultDTO;
import com.sinitek.license.service.ILicModuleService;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模块管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/frontend/api/license/module")
@Tag(name = "模块管理", description = "模块管理相关接口")
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class LicModuleController {

    private ILicModuleService licModuleService;

    @PostMapping("/save")
    @Operation(summary = "保存或更新模块", description = "保存或更新模块信息")
    public RequestResult<Void> saveLicModule(@RequestBody @Validated SaveLicModuleDTO saveLicModuleDTO) {
        licModuleService.saveOrUpdateLicModule(saveLicModuleDTO);
        return new RequestResult<>();
    }

    @GetMapping("/load")
    @Operation(summary = "根据ID加载模块", description = "根据模块ID加载模块详细信息")
    public RequestResult<LoadLicModuleDTO> loadLicModule(@RequestParam @Parameter(description = "模块ID") Long id) {
        LoadLicModuleDTO result = licModuleService.getLicModule(id);
        return new RequestResult<>(result);
    }

    @GetMapping("/find-by-product")
    @Operation(summary = "根据产品ID查找模块", description = "根据产品ID查找该产品下的所有模块")
    public RequestResult<List<LicModuleDTO>> findLicModulesByProductId(@RequestParam @Parameter(description = "产品ID") Long id) {
        List<LicModuleDTO> result = licModuleService.findLicModulesByProductId(id);
        return new RequestResult<>(result);
    }

    @GetMapping("/list")
    @Operation(summary = "分页搜索模块", description = "根据条件分页搜索模块")
    public TableResult<SearchLicModuleResultDTO> searchLicModules(SearchLicModuleDTO searchLicModuleDTO) {
        IPage<SearchLicModuleResultDTO> result = licModuleService.searchLicModules(searchLicModuleDTO);
        return searchLicModuleDTO.build(result);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除模块", description = "删除指定的模块（支持单个或批量）")
    public RequestResult<Void> deleteLicModule(@RequestBody @Parameter(description = "模块ID数组") Long[] ids) {
        licModuleService.deleteLicModules(List.of(ids));
        return new RequestResult<>();
    }


}
