package com.sinitek.license.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicProjectDTO;
import com.sinitek.license.dto.SaveLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectResultDTO;
import com.sinitek.license.service.ILicProjectService;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/frontend/api/license/project")
@Tag(name = "项目管理", description = "项目管理相关接口")
@Setter(onMethod_ = @Autowired)
public class LicProjectController {

    private ILicProjectService licProjectService;

    @PostMapping("/save")
    @Operation(summary = "保存或更新项目", description = "保存或更新项目信息")
    public RequestResult<Void> saveLicProject(@RequestBody @Validated SaveLicProjectDTO saveLicProjectDTO) {
        licProjectService.saveOrUpdateLicProject(saveLicProjectDTO);
        return new RequestResult<>();
    }

    @GetMapping("/load")
    @Operation(summary = "根据ID加载项目", description = "根据项目ID加载项目详细信息")
    public RequestResult<LoadLicProjectDTO> loadLicProject(@RequestParam @Parameter(description = "项目ID") Long id) {
        LoadLicProjectDTO result = licProjectService.getLicProject(id);
        return new RequestResult<>(result);
    }

    @GetMapping("/list")
    @Operation(summary = "分页搜索项目", description = "根据条件分页搜索项目")
    public TableResult<SearchLicProjectResultDTO> searchLicProjects(SearchLicProjectDTO searchLicProjectDTO) {
        IPage<SearchLicProjectResultDTO> result = licProjectService.searchLicProjects(searchLicProjectDTO);
        return searchLicProjectDTO.build(result);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除项目", description = "删除指定的项目（支持单个或批量）")
    public RequestResult<Void> deleteLicProject(@RequestBody @Parameter(description = "项目ID数组") Long[] ids) {
        licProjectService.deleteLicProjects(List.of(ids));
        return new RequestResult<>();
    }

    @GetMapping("/find-options")
    @Operation(summary = "获取项目可选项", description = "获取项目可选项列表，用于下拉选择")
    public RequestResult<List<DataOptionDTO>> findLicProjectOptions() {
        List<DataOptionDTO> result = licProjectService.findLicProjectOptions();
        return new RequestResult<>(result);
    }
}
