package com.sinitek.license.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.LoadLicLicenseDTO;
import com.sinitek.license.dto.SaveLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseDTO;
import com.sinitek.license.dto.SearchLicLicenseResultDTO;
import com.sinitek.license.service.ILicLicenseService;
import com.sinitek.license.util.LicLicenseUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * License管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/frontend/api/license/license")
@Tag(name = "License管理", description = "License管理相关接口")
@Setter(onMethod_ = @Autowired)
public class LicLicenseController {

    private ILicLicenseService licLicenseService;

    @PostMapping("/save")
    @Operation(summary = "保存或更新License", description = "保存或更新License信息")
    public RequestResult<Void> saveLicLicense(@RequestBody @Validated SaveLicLicenseDTO saveLicLicenseDTO) {
        licLicenseService.saveLicLicense(saveLicLicenseDTO);
        return new RequestResult<>();
    }

    @GetMapping("/load")
    @Operation(summary = "根据ID加载License", description = "根据License ID加载License详细信息")
    public RequestResult<LoadLicLicenseDTO> loadLicLicense(@RequestParam @Parameter(description = "License ID") Long id) {
        LoadLicLicenseDTO result = licLicenseService.loadLicLicense(id);
        return new RequestResult<>(result);
    }

    @GetMapping("/list")
    @Operation(summary = "分页搜索License", description = "根据条件分页搜索License")
    public TableResult<SearchLicLicenseResultDTO> searchLicLicenses(SearchLicLicenseDTO searchLicLicenseDTO) {
        IPage<SearchLicLicenseResultDTO> result = licLicenseService.searchLicLicenses(searchLicLicenseDTO);
        return searchLicLicenseDTO.build(result, LicLicenseUtil.searchResultFormat());
    }

    @PostMapping("/delete")
    @Operation(summary = "删除License", description = "删除指定的License（支持单个或批量）")
    public RequestResult<Void> deleteLicLicense(@RequestBody @Parameter(description = "License ID数组") Long[] ids) {
        licLicenseService.deleteLicLicenses(List.of(ids));
        return new RequestResult<>();
    }

    @PostMapping("/publish")
    @Operation(summary = "签发License", description = "签发指定的License")
    public RequestResult<Void> publishLicense(@RequestBody @Parameter(description = "License ID") Long id) {
        licLicenseService.publishLicense(id);
        return new RequestResult<>();
    }

    @PostMapping("/republish")
    @Operation(summary = "续签License", description = "续签指定的License")
    public RequestResult<Void> renewLicense(@RequestBody @Validated SaveLicLicenseDTO renewLicenseDTO) {
        licLicenseService.republishLicense(renewLicenseDTO.getId(), renewLicenseDTO);
        return new RequestResult<>();
    }

    @PostMapping("/terminate")
    @Operation(summary = "终止License", description = "终止指定的License")
    public RequestResult<Void> terminateLicense(@RequestBody @Parameter(description = "License ID") Long id) {
        licLicenseService.terminateLicense(id);
        return new RequestResult<>();
    }

    @GetMapping("/download")
    @Operation(summary = "下载License文件", description = "下载指定License的文件")
    public void downloadLicenseFile(@RequestParam @Parameter(description = "License ID") Long id, HttpServletResponse response) {
        licLicenseService.downloadLicenseFile(id, response);
    }
}
