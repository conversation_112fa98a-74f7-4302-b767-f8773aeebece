package com.sinitek.license.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.license.dto.CopyLicProductDTO;
import com.sinitek.license.dto.LoadLicProductDTO;
import com.sinitek.license.dto.LicProductDTO;
import com.sinitek.license.dto.SaveLicProductDTO;
import com.sinitek.license.dto.SearchLicProductDTO;
import com.sinitek.license.dto.SearchLicProductResultDTO;
import com.sinitek.license.service.ILicProductService;
import com.sinitek.sirm.common.businlog.dto.DataOptionDTO;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 产品管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/frontend/api/license/product")
@Tag(name = "产品管理", description = "产品管理相关接口")
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class LicProductController {

    private ILicProductService licProductService;

    @PostMapping("/save")
    @Operation(summary = "保存或更新产品", description = "保存或更新产品信息")
    public RequestResult<Void> saveLicProduct(@RequestBody @Validated SaveLicProductDTO saveLicProductDTO) {
        licProductService.saveOrUpdateLicProduct(saveLicProductDTO);
        return new RequestResult<>();
    }

    @PostMapping("/copy")
    @Operation(summary = "复制产品", description = "从现有产品复制创建新产品，包括模块信息")
    public RequestResult<Void> copyLicProduct(@RequestBody @Validated CopyLicProductDTO copyLicProductDTO) {
        licProductService.copyLicProduct(copyLicProductDTO);
        return new RequestResult<>();
    }

    @GetMapping("/load")
    @Operation(summary = "根据ID加载产品", description = "根据产品ID加载产品详细信息")
    public RequestResult<LoadLicProductDTO> loadLicProduct(@RequestParam @Parameter(description = "产品ID") Long id) {
        LoadLicProductDTO result = licProductService.getLicProduct(id);
        return new RequestResult<>(result);
    }

    @GetMapping("/find-options")
    @Operation(summary = "获取产品可选项", description = "获取产品可选项列表，用于下拉选择")
    public RequestResult<List<DataOptionDTO>> findLicProductOptions() {
        List<DataOptionDTO> result = licProductService.findLicProductOptions();
        return new RequestResult<>(result);
    }

    @GetMapping("/list")
    @Operation(summary = "分页搜索产品", description = "根据条件分页搜索产品")
    public TableResult<SearchLicProductResultDTO> searchLicProducts(SearchLicProductDTO searchLicProductDTO) {
        IPage<SearchLicProductResultDTO> result = licProductService.searchLicProducts(searchLicProductDTO);
        return searchLicProductDTO.build(result);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除产品", description = "删除指定的产品（支持单个或批量）")
    public RequestResult<Void> deleteLicProduct(@RequestBody @Parameter(description = "产品ID数组") Long[] ids) {
        licProductService.deleteLicProducts(List.of(ids));
        return new RequestResult<>();
    }


}
