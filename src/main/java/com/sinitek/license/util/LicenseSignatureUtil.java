package com.sinitek.license.util;

import lombok.extern.slf4j.Slf4j;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

/**
 * License签名工具类
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
public class LicenseSignatureUtil {
    
    /**
     * 签名算法
     */
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    
    /**
     * RSA算法
     */
    private static final String RSA_ALGORITHM = "RSA";
    
    /**
     * 对数据进行签名
     * 
     * @param data 要签名的数据
     * @param privateKeyStr 私钥字符串（Base64编码）
     * @return 签名结果
     */
    public static byte[] signData(byte[] data, String privateKeyStr) {
        try {
            // 解析私钥
            PrivateKey privateKey = parsePrivateKey(privateKeyStr);
            
            // 创建签名对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data);
            
            byte[] signatureBytes = signature.sign();
            log.info("数据签名成功，数据长度：{} 字节，签名长度：{} 字节", data.length, signatureBytes.length);
            
            return signatureBytes;
            
        } catch (Exception e) {
            log.error("数据签名失败", e);
            throw new RuntimeException("数据签名失败", e);
        }
    }
    
    /**
     * 验证签名
     * 
     * @param data 原始数据
     * @param signatureBytes 签名
     * @param publicKeyStr 公钥字符串（Base64编码）
     * @return 验证结果
     */
    public static boolean verifySignature(byte[] data, byte[] signatureBytes, String publicKeyStr) {
        try {
            // 解析公钥
            PublicKey publicKey = parsePublicKey(publicKeyStr);
            
            // 创建签名验证对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data);
            
            boolean isValid = signature.verify(signatureBytes);
            log.info("签名验证结果：{}", isValid ? "通过" : "失败");
            
            return isValid;
            
        } catch (Exception e) {
            log.error("签名验证失败", e);
            return false;
        }
    }
    
    /**
     * 解析私钥字符串
     */
    private static PrivateKey parsePrivateKey(String privateKeyStr) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }
    
    /**
     * 解析公钥字符串
     */
    private static PublicKey parsePublicKey(String publicKeyStr) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyStr);
        java.security.spec.X509EncodedKeySpec keySpec = new java.security.spec.X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }
}
