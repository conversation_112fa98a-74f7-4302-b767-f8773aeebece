package com.sinitek.license.util;

import com.sinitek.license.dto.LoadLicModuleDTO;
import com.sinitek.license.dto.LicModuleDTO;
import com.sinitek.license.dto.SaveLicModuleDTO;
import com.sinitek.license.dto.SearchLicModuleResultDTO;
import com.sinitek.license.entity.LicModule;
import org.springframework.beans.BeanUtils;

/**
 * 模块转换工具类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public class LicModuleUtil {

    private LicModuleUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * SaveLicModuleDTO转换为LicModule实体
     */
    public static LicModule convertSaveToEntity(SaveLicModuleDTO saveLicModuleDTO) {
        if (saveLicModuleDTO == null) {
            return null;
        }
        LicModule licModule = new LicModule();
        BeanUtils.copyProperties(saveLicModuleDTO, licModule);
        return licModule;
    }

    /**
     * LicModule实体转换为LoadLicModuleDTO
     */
    public static LoadLicModuleDTO convertToLoadDTO(LicModule licModule) {
        if (licModule == null) {
            return null;
        }
        LoadLicModuleDTO loadLicModuleDTO = new LoadLicModuleDTO();
        BeanUtils.copyProperties(licModule, loadLicModuleDTO);
        return loadLicModuleDTO;
    }

    /**
     * LicModule实体转换为LicModuleDTO
     */
    public static LicModuleDTO convertToDTO(LicModule licModule) {
        if (licModule == null) {
            return null;
        }
        LicModuleDTO licModuleDTO = new LicModuleDTO();
        BeanUtils.copyProperties(licModule, licModuleDTO);
        return licModuleDTO;
    }

    /**
     * LicModule实体转换为SearchLicModuleResultDTO
     */
    public static SearchLicModuleResultDTO convertToSearchResultDTO(LicModule licModule) {
        if (licModule == null) {
            return null;
        }
        SearchLicModuleResultDTO resultDTO = new SearchLicModuleResultDTO();
        BeanUtils.copyProperties(licModule, resultDTO);
        return resultDTO;
    }
}
