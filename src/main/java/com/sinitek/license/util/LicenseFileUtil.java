package com.sinitek.license.util;

import cn.hutool.core.io.FileUtil;
import com.sinitek.license.dto.LicenseFileDTO;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import java.io.File;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * License二进制文件工具类
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
public class LicenseFileUtil {
    
    /**
     * 魔数：0x7369726D（含义为sirm）
     */
    private static final int MAGIC_NUMBER = 0x7369726D;
    
    /**
     * 保留字段长度
     */
    private static final int RESERVED_LENGTH = 8;

    /**
     * 生成License临时文件
     *
     * @param licenseFileDTO License二进制文件DTO
     * @param fileName 文件名
     * @return 生成的临时文件
     */
    public static File generateLicenseTempFile(LicenseFileDTO licenseFileDTO, String fileName) {

        byte[] bytes = generateLicenseBinaryFile(licenseFileDTO);

        String tempDir = SettingUtils.getTempDir();

        // 生成license文件（确保文件名已包含.lic后缀）
        String fullFileName = fileName.endsWith(".lic") ?
            fileName : fileName + ".lic";

        File licenseFile = Paths.get(tempDir, fullFileName).toFile();

        try {
            // 使用hutool将字节数组写入文件
            FileUtil.writeBytes(bytes, licenseFile);
            log.info("License临时文件生成成功：{}", licenseFile.getAbsolutePath());
            return licenseFile;
        } catch (Exception e) {
            log.error("生成License临时文件失败，文件路径：{}", licenseFile.getAbsolutePath(), e);
            throw new RuntimeException("生成License临时文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 生成License二进制文件
     * 
     * @param licenseFileDTO License二进制文件DTO
     * @return 二进制文件内容
     */
    public static byte[] generateLicenseBinaryFile(LicenseFileDTO licenseFileDTO) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            // 1. 写入魔数（4字节）
            outputStream.write(intToBytes(MAGIC_NUMBER));
            
            // 2. 写入保留字段（8字节）
            outputStream.write(new byte[RESERVED_LENGTH]);
            
            // 3. 计算数据区总长度
            byte[] data = licenseFileDTO.getData();
            byte[] signature = licenseFileDTO.getSignature();
            byte[] publicKey = licenseFileDTO.getPublicKey();
            
            int totalDataLength = 4 + data.length + 4 + signature.length + 4 + publicKey.length;
            
            // 4. 写入数据区长度（4字节）
            outputStream.write(intToBytes(totalDataLength));
            
            // 5. 写入数据长度（4字节）
            outputStream.write(intToBytes(data.length));
            
            // 6. 写入数据（变长）
            outputStream.write(data);
            
            // 7. 写入签名长度（4字节）
            outputStream.write(intToBytes(signature.length));
            
            // 8. 写入签名（变长）
            outputStream.write(signature);
            
            // 9. 写入公钥长度（4字节）
            outputStream.write(intToBytes(publicKey.length));
            
            // 10. 写入公钥（变长）
            outputStream.write(publicKey);
            
            byte[] result = outputStream.toByteArray();
            log.info("成功生成License二进制文件，文件大小：{} 字节", result.length);
            
            return result;
            
        } catch (IOException e) {
            log.error("生成License二进制文件失败", e);
            throw new RuntimeException("生成License二进制文件失败", e);
        }
    }
    
    /**
     * 将int转换为4字节的byte数组（大端序）
     */
    private static byte[] intToBytes(int value) {
        return ByteBuffer.allocate(4)
                .order(ByteOrder.BIG_ENDIAN)
                .putInt(value)
                .array();
    }

}
