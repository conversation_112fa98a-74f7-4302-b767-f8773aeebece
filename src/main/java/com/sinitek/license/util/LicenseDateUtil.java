package com.sinitek.license.util;

import com.sinitek.license.enumerate.LicenseTypeEnum;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * License日期计算工具类
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public class LicenseDateUtil {

    private LicenseDateUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 计算剩余天数（支持永久版判断）
     *
     * @param licenseType License类型
     * @param validEndTime 过期时间
     * @return 剩余天数字符串
     */
    public static String calculateSurplusDay(Integer licenseType, Date validEndTime) {
        // 永久版显示永久有效
        if (licenseType != null && licenseType.equals(LicenseTypeEnum.PERMANENT.getValue())) {
            return "永久有效";
        }

        // 其他版本计算剩余天数
        if (validEndTime == null) {
            return null;
        }

        LocalDate now = LocalDate.now();
        LocalDate endDate = validEndTime.toInstant()
            .atZone(ZoneId.systemDefault()).toLocalDate();
        long surplusDay = ChronoUnit.DAYS.between(now, endDate);

        if (surplusDay < 0) {
            return "已过期";
        } else {
            return String.format("%d天", surplusDay);
        }
    }

    /**
     * 计算剩余天数（数值类型，用于首页统计等场景）
     *
     * @param licenseType License类型
     * @param validEndTime 过期时间
     * @return 剩余天数（永久版返回Long.MAX_VALUE，已过期返回负数）
     */
    public static Long calculateSurplusDayNumber(Integer licenseType, Date validEndTime) {
        // 永久版返回最大值
        if (licenseType != null && licenseType.equals(LicenseTypeEnum.PERMANENT.getValue())) {
            return Long.MAX_VALUE;
        }

        // 其他版本计算剩余天数
        if (validEndTime == null) {
            return null;
        }

        LocalDate now = LocalDate.now();
        LocalDate endDate = validEndTime.toInstant()
            .atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(now, endDate);
    }
}
