package com.sinitek.license.util;

import com.sinitek.license.enumerate.ExpireStrategyEnum;
import com.sinitek.license.enumerate.LicenseStatusEnum;
import com.sinitek.license.enumerate.LicenseTypeEnum;

/**
 * 枚举工具类
 * 统一管理枚举的获取方法
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class LicenseEnumUtil {

    private LicenseEnumUtil() {
        // 私有构造函数，防止实例化
    }

    // ==================== License类型枚举相关方法 ====================

    /**
     * 根据值获取License类型枚举
     */
    public static LicenseTypeEnum getLicenseTypeEnum(Integer value) {
        return LicenseTypeEnum.fromValue(value);
    }

    /**
     * 根据值获取License类型名称
     */
    public static String getLicenseTypeName(Integer value) {
        LicenseTypeEnum typeEnum = LicenseTypeEnum.fromValue(value);
        return typeEnum != null ? typeEnum.getName() : null;
    }

    // ==================== License状态枚举相关方法 ====================

    /**
     * 根据值获取License状态枚举
     */
    public static LicenseStatusEnum getLicenseStatusEnum(Integer value) {
        return LicenseStatusEnum.fromValue(value);
    }

    /**
     * 根据值获取License状态名称
     */
    public static String getLicenseStatusName(Integer value) {
        LicenseStatusEnum statusEnum = LicenseStatusEnum.fromValue(value);
        return statusEnum != null ? statusEnum.getName() : null;
    }

    // ==================== 过期策略枚举相关方法 ====================

    /**
     * 根据值获取过期策略枚举
     */
    public static ExpireStrategyEnum getExpireStrategyEnum(Integer value) {
        return ExpireStrategyEnum.fromValue(value);
    }

    /**
     * 根据值获取过期策略名称
     */
    public static String getExpireStrategyName(Integer value) {
        ExpireStrategyEnum strategyEnum = ExpireStrategyEnum.fromValue(value);
        return strategyEnum != null ? strategyEnum.getName() : null;
    }

    // ==================== 通用枚举获取方法 ====================

    /**
     * 通用的枚举名称获取方法
     * 支持所有实现了getValue()和getName()方法的枚举
     * 
     * @param enumClass 枚举类
     * @param value 枚举值
     * @param <T> 枚举类型
     * @return 枚举名称，如果未找到返回null
     */
    public static <T extends Enum<T>> String getEnumName(Class<T> enumClass, Integer value) {
        if (value == null || enumClass == null) {
            return null;
        }

        try {
            T[] enumConstants = enumClass.getEnumConstants();
            for (T enumConstant : enumConstants) {
                // 通过反射调用getValue方法
                Integer enumValue = (Integer) enumConstant.getClass().getMethod("getValue").invoke(enumConstant);
                if (value.equals(enumValue)) {
                    // 通过反射调用getName方法
                    return (String) enumConstant.getClass().getMethod("getName").invoke(enumConstant);
                }
            }
        } catch (Exception e) {
            // 如果反射调用失败，返回null
            return null;
        }

        return null;
    }

    /**
     * 通用的枚举获取方法
     * 支持所有实现了getValue()方法的枚举
     * 
     * @param enumClass 枚举类
     * @param value 枚举值
     * @param <T> 枚举类型
     * @return 枚举实例，如果未找到返回null
     */
    public static <T extends Enum<T>> T getEnum(Class<T> enumClass, Integer value) {
        if (value == null || enumClass == null) {
            return null;
        }

        try {
            T[] enumConstants = enumClass.getEnumConstants();
            for (T enumConstant : enumConstants) {
                // 通过反射调用getValue方法
                Integer enumValue = (Integer) enumConstant.getClass().getMethod("getValue").invoke(enumConstant);
                if (value.equals(enumValue)) {
                    return enumConstant;
                }
            }
        } catch (Exception e) {
            // 如果反射调用失败，返回null
            return null;
        }

        return null;
    }
}
