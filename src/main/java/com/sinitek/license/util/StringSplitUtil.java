package com.sinitek.license.util;

import org.springframework.util.StringUtils;

/**
 * 字符串分割工具类
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public class StringSplitUtil {

    private StringSplitUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 使用中英文逗号分割字符串
     *
     * @param str 待分割的字符串
     * @return 分割后的字符串数组（已去除空白）
     */
    public static String[] splitByComma(String str) {
        if (!StringUtils.hasText(str)) {
            return new String[0];
        }

        // 使用中英文逗号分割
        String[] parts = str.split("[,，]+");
        
        // 去除每个部分的空白字符
        for (int i = 0; i < parts.length; i++) {
            parts[i] = parts[i].trim();
        }
        
        return parts;
    }

    /**
     * 使用中英文逗号、分号或空格分割字符串
     *
     * @param str 待分割的字符串
     * @return 分割后的字符串数组（已去除空白）
     */
    public static String[] splitByCommaOrSemicolonOrSpace(String str) {
        if (!StringUtils.hasText(str)) {
            return new String[0];
        }

        // 使用中英文逗号、分号或空格分割
        String[] parts = str.split("[,，;；\\s]+");
        
        // 去除每个部分的空白字符
        for (int i = 0; i < parts.length; i++) {
            parts[i] = parts[i].trim();
        }
        
        return parts;
    }
}
