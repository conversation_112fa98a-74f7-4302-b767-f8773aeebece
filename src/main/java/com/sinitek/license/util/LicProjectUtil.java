package com.sinitek.license.util;

import com.sinitek.license.dto.LoadLicProjectDTO;
import com.sinitek.license.dto.SaveLicProjectDTO;
import com.sinitek.license.dto.SearchLicProjectResultDTO;
import com.sinitek.license.entity.LicProject;
import org.springframework.beans.BeanUtils;

/**
 * 项目转换工具类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public class LicProjectUtil {

    private LicProjectUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * SaveLicProjectDTO转换为LicProject实体
     */
    public static LicProject convertSaveToEntity(SaveLicProjectDTO saveLicProjectDTO) {
        if (saveLicProjectDTO == null) {
            return null;
        }
        LicProject licProject = new LicProject();
        BeanUtils.copyProperties(saveLicProjectDTO, licProject);
        return licProject;
    }

    /**
     * LicProject实体转换为LoadLicProjectDTO
     */
    public static LoadLicProjectDTO convertToLoadDTO(LicProject licProject) {
        if (licProject == null) {
            return null;
        }
        LoadLicProjectDTO loadLicProjectDTO = new LoadLicProjectDTO();
        BeanUtils.copyProperties(licProject, loadLicProjectDTO);
        return loadLicProjectDTO;
    }

    /**
     * LicProject实体转换为SearchLicProjectResultDTO
     */
    public static SearchLicProjectResultDTO convertToSearchResultDTO(LicProject licProject) {
        if (licProject == null) {
            return null;
        }
        SearchLicProjectResultDTO resultDTO = new SearchLicProjectResultDTO();
        BeanUtils.copyProperties(licProject, resultDTO);
        return resultDTO;
    }
}
