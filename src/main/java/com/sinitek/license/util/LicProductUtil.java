package com.sinitek.license.util;

import com.sinitek.license.dto.LoadLicProductDTO;
import com.sinitek.license.dto.LicProductDTO;
import com.sinitek.license.dto.SaveLicProductDTO;
import com.sinitek.license.dto.SearchLicProductResultDTO;
import com.sinitek.license.entity.LicProduct;
import org.springframework.beans.BeanUtils;

/**
 * 产品转换工具类
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public class LicProductUtil {

    private LicProductUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * SaveLicProductDTO转换为LicProduct实体
     */
    public static LicProduct convertSaveToEntity(SaveLicProductDTO saveLicProductDTO) {
        if (saveLicProductDTO == null) {
            return null;
        }
        LicProduct licProduct = new LicProduct();
        BeanUtils.copyProperties(saveLicProductDTO, licProduct);
        return licProduct;
    }

    /**
     * LicProduct实体转换为LoadLicProductDTO
     */
    public static LoadLicProductDTO convertToLoadDTO(LicProduct licProduct) {
        if (licProduct == null) {
            return null;
        }
        LoadLicProductDTO loadLicProductDTO = new LoadLicProductDTO();
        BeanUtils.copyProperties(licProduct, loadLicProductDTO);
        return loadLicProductDTO;
    }

    /**
     * LicProduct实体转换为LicProductDTO
     */
    public static LicProductDTO convertToDTO(LicProduct licProduct) {
        if (licProduct == null) {
            return null;
        }
        LicProductDTO licProductDTO = new LicProductDTO();
        BeanUtils.copyProperties(licProduct, licProductDTO);
        return licProductDTO;
    }

    /**
     * LicProduct实体转换为SearchLicProductResultDTO
     */
    public static SearchLicProductResultDTO convertToSearchResultDTO(LicProduct licProduct) {
        if (licProduct == null) {
            return null;
        }
        SearchLicProductResultDTO resultDTO = new SearchLicProductResultDTO();
        BeanUtils.copyProperties(licProduct, resultDTO);
        return resultDTO;
    }

    /**
     * 构建产品标签（产品名称 - 产品版本）
     */
    public static String buildProductLabel(LicProduct product) {
        if (product == null) {
            return null;
        }
        return buildProductLabel(product.getName(), product.getProductVersion());
    }

    /**
     * 构建产品标签（产品名称 - 产品版本）
     */
    public static String buildProductLabel(String productName, String productVersion) {
        if (productName == null || productVersion == null) {
            return null;
        }
        return productName + " - " + productVersion;
    }
}
