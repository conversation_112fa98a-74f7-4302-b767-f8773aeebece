spring:
  cloud:
    nacos:
      config:
        server-addr: **************:28848
        enabled: true
        file-extension: yaml
        namespace: f23052ec-008f-4293-8ea1-1cb0ac8d158c
        ## 指定要加载的配置文件名称
#        prefix: sirmapp-80-test
        username: nacos
        password: nacos
      discovery:
        server-addr: **************:28848
        ip: **************
        port: 18093
        enabled: true
        namespace: f23052ec-008f-4293-8ea1-1cb0ac8d158c
        username: nacos
        password: nacos