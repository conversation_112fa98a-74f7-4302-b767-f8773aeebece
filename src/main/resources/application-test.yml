cacheManager: redisCacheManager
logon:
  code: false
sirm:
  encrypt:
    signDebug: true
    debug: true

spring:
  cloud:
    nacos:
      discovery:
        enabled: false

  redis:
    redisson:
      config: |
        singleServerConfig:
          address: "redis://**************:19068"
          password: WbABw2
          database: 6
          timeout: 10000
          connectionMinimumIdleSize: 10
          connectionPoolSize: 500
        threads: 100
        nettyThreads: 100
        transportMode: "NIO"

  datasource:
    username: root
    password: ENC(fYkVQUE4onEHUU+X8DtXdrQhceAePOd/zl+EfyhyIYjJSqtt4Iw129EPPrS2PRRS)
    url: ***********************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # 连接池的配置信息
    druid:
      # 初始化大小，最小，最大
      initialSize: 10
      minIdle: 1
      maxActive: 600
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,log4j
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: ENC(4ckYP6jM9l8595MZcvVa3JN5Pp9iqyHLn23hzZQsE816S3k61GyT60kwbmuwQ8CA)
        allow:
      web-stat-filter:
        enabled: true

sinicube:
  cookie:
    secure: false
  business-log:
    mode: db
  # # 子系统相关参数
  # subsystem:
  #   # 子前端系统地址
  #   # {host}占位符会被解析为主前端host地址
  #   sub-frontend-address:
  #     - http://*************:32287/
  api-security:
    ## 默认值为false
    enable: false
  exception:
    ## 是否打印BussinessException的报错日志,默认为false
    business-print-log: false
  user:
    ## 忘记密码相关配置
    forget-pwd:
      enable: true
  ## 加密算法配置
  encryption-algorithm:
    ## 非对称加密算法的配置
    asymmetric:
      algorithm: SM2
      rsa:
        defaultKey:
          privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
          publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'
      sm2:
        defaultKey:
          privateKey: '1f0be4be2e9f8510ef5be39a0945bd2535bdfe451a02f111e2a2908bfec178d0'
          publicKey: '047807aa2d0cfe90886e3a18ef5402f48ef1fdb1791b14b9cee8ba3f9630f5ac7e1622fd2752137f0d4aec82c66a52b69b280443bf9decc73f46137fe3dc602e9a'
    ## 对称加密散发的配置
    symmetry:
      algorithm: SM4
      aes:
        defaultKey: 'sirmpasswordcryp'
      sm4:
        defaultKey: '4a65463855397748464f4d6673323139'
    ## 散列加密算法配置
    hash:
      algorithm: SM3

  message:
    ## 我的消息是否显示邮件发送方式, 默认为true
    myMsgShowEmail: true
    independent: false
    service-name: CLOUD-SIRMAPP
    email:
      limit:
        ## 邮件发送限流配置
        ## 间隔时间内，最大发送次数
        rate: 30
        ## 间隔时间，单位 秒
        interval: 60
      from-sys: east1000@*************
      smtp-server: *************
      smtpUser: east1033@*************
      smtpPwd: ENC(BXPF+XoCGpmMk7Z2U6rPTReiekSHAmo8COz98Lq2qNUwXmm/turZ39Ai1b41hUtM)
    ## 发送消息队列批次数，默认50
    batchSize: 50
    retry:
      ## 消息发送失败重试配置，是否开启重试，默认开启
      enable: true
      ## 最大重试次数，默认3次
      maxAttempts: 3
  ## 缓存配置
  cache:
    prefix: "sini-cube-75:"
    defaultCache: maximumSize=1000,expireAfterWrite=86400s
    customizeCache:
      - usersessionCache: maximumSize=800,expireAfterWrite=1800s
      - captchaCache: maximumSize=500,expireAfterWrite=60s
      - keyPairCache: maximumSize=200
      - exportProgressCache: maximumSize=200,expireAfterWrite=1800s
      - verificationCodeCache: maximumSize=1000,expireAfterWrite=300s
      - sendMessageJobCache: maximumSize=100,expireAfterWrite=300s

  minio:
    endpoint: **************
    port: 29529
    accessKey: sinitek
    secretKey: sinitek123
    secure: false
    bucketName: "sinicube-latest-test"
    region: cn-shanghai
  attachment:
    #附件存放方式 : db、file、fastDFS、minio
    store-type: minio
  multi-factor-check:
    enable: false
    types:
      - 0
      - 1
    strategy: any
    time-step: 120
  #  身份验证失效安全性配置，配置为true时，用户登录会话过期直接跳转登录页，注：前端框架如果没有轮询后端接口，此配置失效
  user-session-expire-redirection: false
usercheck:
  loalcheckuser: admin

# knife4j相关的配置
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    username: admin
    password: ENC(HanXsHPCTl1wCafr5FKErh1eDgjGgUcEW+20HFcnpATLRSfodLGJzIJjPVsNdx3u)


# actuator暴露监控端点
management:
  endpoints:
    enabled-by-default: false #关闭监控
    web:
      exposure:
        include: '*'
