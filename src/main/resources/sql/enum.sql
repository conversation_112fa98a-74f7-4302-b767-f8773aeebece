## license状态枚举
delete from sirm_enum where catalog = 'LIC' and type = 'license-status';
INSERT INTO sirm_enum
(objid, `catalog`, `type`, name, value, strvalue, description, sort, createtimestamp, updatetimestamp, version, entityname)
VALUES
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-status', '未签发', 1, NULL, '同一数据最多有一条记录处于该状态', 1, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-status', '已签发', 2, NULL, '同一数据最多有一条记录处于该状态', 2, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-status', '已终止', 5, NULL, NULL, 3, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM');

## license类型枚举
delete from sirm_enum where catalog = 'LIC' and type = 'license-type';
INSERT INTO sirm_enum
(objid, `catalog`, `type`, name, value, strvalue, description, sort, createtimestamp, updatetimestamp, version, entityname)
VALUES
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-type', '试用版', 1, NULL, NULL, 1, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-type', '订阅版', 2, NULL, NULL, 2, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'license-type', '永久版', 3, NULL, NULL, 3, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM');

## license提醒策略枚举
delete from sirm_enum where catalog = 'LIC' and type = 'expire-strategy';
INSERT INTO sirm_enum
(objid, `catalog`, `type`, name, value, strvalue, description, sort, createtimestamp, updatetimestamp, version, entityname)
VALUES
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'expire-strategy', '7天连续提醒', 1, NULL, NULL, 1, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'expire-strategy', '15天阶梯提醒', 2, NULL, NULL, 2, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM'),
((SELECT max_objid + 1 FROM (SELECT MAX(objid) AS max_objid FROM sirm_enum) AS temp_table),
       'LIC', 'expire-strategy', '30天阶梯提醒', 3, NULL, NULL, 3, '2025-05-21 08:00:00', '2025-05-12 08:00:00', 1, 'SIRMENUM');

