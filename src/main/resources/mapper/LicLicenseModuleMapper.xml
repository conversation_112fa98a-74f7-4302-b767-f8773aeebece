<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.license.mapper.LicLicenseModuleMapper">

    <!-- 根据License ID查找模块关联 -->
    <select id="findByLicenseId" resultType="com.sinitek.license.entity.LicLicenseModule">
        SELECT 
            id,
            createtimestamp,
            updatetimestamp,
            version,
            valid_begin_time,
            valid_end_time,
            license_id,
            module_id
        FROM lic_license_module
        WHERE license_id = #{licenseId}
        ORDER BY id ASC
    </select>

</mapper>
