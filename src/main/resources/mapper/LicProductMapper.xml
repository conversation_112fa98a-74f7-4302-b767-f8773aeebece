<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.license.mapper.LicProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sinitek.license.entity.LicProduct">
        <id column="id" property="id" />
        <result column="createtimestamp" property="createTimeStamp" />
        <result column="updatetimestamp" property="updateTimeStamp" />
        <result column="version" property="version" />
        <result column="name" property="name" />
        <result column="product_version" property="productVersion" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, createtimestamp, updatetimestamp, version, name, product_version, description
    </sql>

    <!-- 分页搜索产品 -->
    <select id="searchLicProducts" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM lic_product
        <where>
            <if test="dto.name != null and dto.name != ''">
                AND name LIKE CONCAT('%', #{dto.name}, '%')
            </if>
            <if test="dto.productVersion != null and dto.productVersion != ''">
                AND product_version LIKE CONCAT('%', #{dto.productVersion}, '%')
            </if>
            <if test="dto.description != null and dto.description != ''">
                AND description LIKE CONCAT('%', #{dto.description}, '%')
            </if>
        </where>
        ORDER BY createtimestamp DESC
    </select>

</mapper>
