<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.license.mapper.LicProjectMapper">

    <!-- 分页搜索项目 -->
    <select id="searchLicProjects" resultType="com.sinitek.license.entity.LicProject">
        SELECT 
            p.id,
            p.createtimestamp,
            p.updatetimestamp,
            p.version,
            p.name,
            p.product_id,
            p.customer_name,
            p.project_manager,
            p.project_manager_email,
            p.customer_manager,
            p.customer_manager_email,
            p.receive_email,
            p.description
        FROM lic_project p
        <where>
            <if test="searchParam.name != null and searchParam.name != ''">
                AND p.name LIKE CONCAT('%', #{searchParam.name}, '%')
            </if>
            <if test="searchParam.productId != null">
                AND p.product_id = #{searchParam.productId}
            </if>
            <if test="searchParam.customerName != null and searchParam.customerName != ''">
                AND p.customer_name LIKE CONCAT('%', #{searchParam.customerName}, '%')
            </if>
        </where>
        ORDER BY p.name ASC
    </select>

</mapper>
