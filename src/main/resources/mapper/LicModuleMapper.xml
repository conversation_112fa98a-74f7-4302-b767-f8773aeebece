<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.license.mapper.LicModuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sinitek.license.entity.LicModule">
        <id column="id" property="id" />
        <result column="createtimestamp" property="createTimeStamp" />
        <result column="updatetimestamp" property="updateTimeStamp" />
        <result column="version" property="version" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="route" property="route" />
        <result column="limit_param" property="limitParam" />
        <result column="description" property="description" />
        <result column="product_id" property="productId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, createtimestamp, updatetimestamp, version, name, code, route, limit_param, description, product_id
    </sql>

    <!-- 分页搜索模块 -->
    <select id="searchLicModules" resultMap="BaseResultMap">
        SELECT
        m.id, m.createtimestamp, m.updatetimestamp, m.version, m.name, m.code, 
        m.route, m.limit_param, m.description, m.product_id
        FROM lic_module m
        LEFT JOIN lic_product p ON m.product_id = p.id
        <where>
            <if test="dto.name != null and dto.name != ''">
                AND m.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>
            <if test="dto.code != null and dto.code != ''">
                AND m.code LIKE CONCAT('%', #{dto.code}, '%')
            </if>
            <if test="dto.route != null and dto.route != ''">
                AND m.route LIKE CONCAT('%', #{dto.route}, '%')
            </if>
            <if test="dto.productId != null">
                AND m.product_id = #{dto.productId}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                AND p.name LIKE CONCAT('%', #{dto.productName}, '%')
            </if>
        </where>
        ORDER BY m.createtimestamp DESC
    </select>

    <!-- 根据产品ID查找模块列表 -->
    <select id="findLicModulesByProductId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM lic_module
        WHERE product_id = #{productId}
        ORDER BY createtimestamp DESC
    </select>

</mapper>
