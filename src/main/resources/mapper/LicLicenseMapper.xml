<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.license.mapper.LicLicenseMapper">

    <!-- 分页搜索License -->
    <select id="searchLicLicenses" resultType="com.sinitek.license.dto.SearchLicLicenseResultDTO">
        SELECT 
            l.*,
            p.name projectName,
            p.customer_name customerName,
            prod.name productName,
            prod.product_version productVersion
        FROM lic_license l
        LEFT JOIN lic_project p ON l.project_id = p.id
        LEFT JOIN lic_product prod ON p.product_id = prod.id
        <where>
            l.thread_latest_flag = 1
            <if test="searchParam.projectId != null">
                AND p.id = #{searchParam.projectId}
            </if>
            <if test="searchParam.productId != null">
                AND p.product_id = #{searchParam.productId}
            </if>
            <if test="searchParam.customerName != null and searchParam.customerName != ''">
                AND p.customer_name LIKE CONCAT('%', #{searchParam.customerName}, '%')
            </if>
            <if test="searchParam.type != null">
                AND l.type = #{searchParam.type}
            </if>
            <if test="searchParam.publishStatus != null">
                AND l.publish_status = #{searchParam.publishStatus}
            </if>
            <if test="searchParam.validEndTimeStart != null">
                AND l.valid_end_time >= #{searchParam.validEndTimeStart}
            </if>
            <if test="searchParam.validEndTimeEnd != null">
                AND l.valid_end_time &lt;= #{searchParam.validEndTimeEnd}
            </if>
        </where>
        ORDER BY p.name ASC
    </select>

</mapper>
