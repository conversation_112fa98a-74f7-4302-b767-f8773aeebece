-- 创建项目表
CREATE TABLE `lic_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createtimestamp` datetime NOT NULL COMMENT '创建时间',
  `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `name` varchar(50) NOT NULL COMMENT '项目名称',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户名称',
  `project_manager` varchar(50) NOT NULL COMMENT '项目经理',
  `project_manager_email` varchar(100) NOT NULL COMMENT '项目经理邮箱',
  `customer_manager` varchar(50) NOT NULL COMMENT '客户经理',
  `customer_manager_email` varchar(100) NOT NULL COMMENT '客户经理邮箱',
  `receive_email` varchar(3000) DEFAULT NULL COMMENT '接收提醒消息邮箱',
  `description` varchar(500) DEFAULT NULL COMMENT '项目描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lic_project_name` (`name`),
  KEY `idx_lic_project_product_id` (`product_id`),
  KEY `idx_lic_project_customer_name` (`customer_name`),
  CONSTRAINT `fk_lic_project_product_id` FOREIGN KEY (`product_id`) REFERENCES `lic_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';
