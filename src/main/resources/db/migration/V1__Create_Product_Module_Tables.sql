-- 创建产品表
CREATE TABLE `lic_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createtimestamp` datetime NOT NULL COMMENT '创建时间',
  `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `name` varchar(50) NOT NULL COMMENT '产品名称',
  `product_version` varchar(50) NOT NULL COMMENT '产品版本',
  `description` varchar(500) DEFAULT NULL COMMENT '产品描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lic_product_id` (`id`),
  KEY `idx_lic_product_name` (`name`),
  KEY `idx_lic_product_version` (`product_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 创建模块表
CREATE TABLE `lic_module` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createtimestamp` datetime NOT NULL COMMENT '创建时间',
  `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `name` varchar(50) NOT NULL COMMENT '模块名称',
  `code` varchar(50) NOT NULL COMMENT '模块代码',
  `route` varchar(50) NOT NULL COMMENT '路由',
  `limit_param` varchar(500) DEFAULT NULL COMMENT '限制参数',
  `description` varchar(500) DEFAULT NULL COMMENT '模块描述',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lic_module_id` (`id`),
  UNIQUE KEY `uk_lic_module_product_id_name` (`product_id`,`name`),
  UNIQUE KEY `uk_lic_module_product_id_code` (`product_id`,`code`),
  KEY `idx_lic_module_product_id` (`product_id`),
  KEY `idx_lic_module_name` (`name`),
  KEY `idx_lic_module_code` (`code`),
  CONSTRAINT `fk_lic_module_product_id` FOREIGN KEY (`product_id`) REFERENCES `lic_product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块表';

-- 插入示例数据
INSERT INTO `lic_product` (`createtimestamp`, `updatetimestamp`, `version`, `name`, `product_version`, `description`) VALUES
(NOW(), NOW(), 0, 'SinicubeFramework', '8.0.0', 'Sinicube开发框架'),
(NOW(), NOW(), 0, 'SirmApp', '8.0.0', 'Sirm应用平台');

INSERT INTO `lic_module` (`createtimestamp`, `updatetimestamp`, `version`, `name`, `code`, `route`, `limit_param`, `description`, `product_id`) VALUES
(NOW(), NOW(), 0, '组织管理', 'ORG_MANAGE', '/org', '{"maxOrgCount": 5}', '组织架构管理模块', 1),
(NOW(), NOW(), 0, '用户管理', 'USER_MANAGE', '/user', '{"maxUserCount": 100}', '用户管理模块', 1),
(NOW(), NOW(), 0, '权限管理', 'PERMISSION_MANAGE', '/permission', NULL, '权限管理模块', 1),
(NOW(), NOW(), 0, '工作流', 'WORKFLOW', '/workflow', '{"maxProcessCount": 10}', '工作流引擎模块', 2),
(NOW(), NOW(), 0, '报表管理', 'REPORT_MANAGE', '/report', '{"maxReportCount": 20}', '报表管理模块', 2);
