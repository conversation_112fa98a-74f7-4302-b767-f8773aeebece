-- 创建License表
CREATE TABLE `lic_license` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createtimestamp` datetime NOT NULL COMMENT '创建时间',
  `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  -- 版本模型字段
  `publish_version` int NOT NULL DEFAULT '1' COMMENT '发布版本',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-未签发，2-已签发，5-已终止',
  `thread_id` bigint NOT NULL COMMENT '线索ID',
  `thread_latest_flag` tinyint NOT NULL DEFAULT '1' COMMENT '是否最新记录',
  `publish_date` datetime DEFAULT NULL COMMENT '发布时间',
  `latest_flag` tinyint NOT NULL DEFAULT '1' COMMENT '是否最新有效记录',
  -- 业务字段
  `type` int NOT NULL COMMENT 'License类型：1-试用版，2-订阅版，3-永久版',
  `valid_begin_time` datetime NOT NULL COMMENT '生效时间',
  `valid_end_time` datetime NOT NULL COMMENT '到期时间',
  `mac_address` varchar(3000) DEFAULT NULL COMMENT '授权设备MAC地址，多个地址用逗号分隔',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `private_key` text DEFAULT NULL COMMENT '私钥',
  `public_key` text DEFAULT NULL COMMENT '公钥',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  PRIMARY KEY (`id`),
  KEY `idx_lic_license_project_id` (`project_id`),
  KEY `idx_lic_license_thread_id` (`thread_id`),
  KEY `idx_lic_license_valid_end_time` (`valid_end_time`),
  KEY `idx_lic_license_publish_status` (`publish_status`),
  KEY `idx_lic_license_thread_latest_flag` (`thread_latest_flag`),
  CONSTRAINT `fk_lic_license_project_id` FOREIGN KEY (`project_id`) REFERENCES `lic_project` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='License表';

-- 创建License模块关联表
CREATE TABLE `lic_license_module` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createtimestamp` datetime NOT NULL COMMENT '创建时间',
  `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `valid_begin_time` datetime NOT NULL COMMENT '模块授权生效时间',
  `valid_end_time` datetime NOT NULL COMMENT '模块授权到期时间',
  `license_id` bigint NOT NULL COMMENT 'License ID',
  `module_id` bigint NOT NULL COMMENT '模块ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lic_license_module` (`license_id`, `module_id`),
  KEY `idx_lic_license_module_license_id` (`license_id`),
  KEY `idx_lic_license_module_module_id` (`module_id`),
  CONSTRAINT `fk_lic_license_module_license_id` FOREIGN KEY (`license_id`) REFERENCES `lic_license` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_lic_license_module_module_id` FOREIGN KEY (`module_id`) REFERENCES `lic_module` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='License模块关联表';
