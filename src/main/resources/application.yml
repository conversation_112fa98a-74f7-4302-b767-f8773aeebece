server:
  port: 8096
  servlet:
    encoding:
      charset: utf-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  main:
    ## 允许Spring循环引用
    allow-circular-references: true
  mvc:
    servlet:
      load-on-startup: 1
  application:
    name: CLOUD-SIRMAPP

  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: NEVER
    properties:
      # 调度标识名,集群中每一个实例都必须使用相同的名称
      org.quartz.scheduler.instanceName: MyQuartzScheduler
      org.quartz.scheduler.instanceId: AUTO
      org.quartz.threadPool.threadCount: 15
      org.quartz.jobStore.isClustered: true
      org.quartz.jobStore.clusterCheckinInterval: 15000
      org.quartz.jobStore.misfireThreshold: 60000

mybatis-plus:
  configuration:
    # 开始状态打印Mybatis日志,默认注释
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: 'null' #注意：单引号
  # 扫描该包下的 typehandler类，注册到Mybatis
  typeHandlersPackage: com.sinitek.data.mybatis.typehandlers
  global-config:
    # 是否关闭MP3.0自带的banner
    banner: true
    # 雪花算法使用的 worker-id、datacenter-id,集群环境下不同机器需要不同
    worker-id: 1
    datacenter-id: 1
    db-config:
      # keepGlobalFormat=true的字段会根据该值进行String.format,主要处理数据库关键字
      # '`%s`' 为mysql,  '%s' 为 oracle
      column-format: '`%s`'
      #主键类型
      id-type: 3
      #是否开启自定义的 IDGenerator策略
      id-customer-enable: false
      #是否开启兼容EntityName(使用Metadb_Entity表的记录进行兼容)
      compatible-entityname-enable: true
      #更新的时候是否判断设置为null,默认是跳过 null的更新的。现在设置为 null 也是可以更新。
      update-strategy: ignored

feign:
  hystrix:
    enabled: true

sinicube:
  independent: false
  cloud:
    ## 设置当前的服务类型, MASTER(主服务) 、MICROSERVICE(子服务,不配置默认为子服务)
    type: MASTER
  message:
    independent: false

  ## 加密算法配置
  encryption-algorithm:
    ## 非对称加密算法的配置
    asymmetric:
      algorithm: RSA
      rsa:
        defaultKey:
          privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
          publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'
    symmetry:
      algorithm: AES
      aes:
        defaultKey: 'sirmpasswordcryp'

  export:
    ## 全部导出获取接口数据时,是否使用系统中的主机域名地址作为前缀,默认为false
    use-host-address: false

  csrf:
    ## 是否启用跨站点请求验证,默认为false
    enable: false

    ## 信任的白名单列表
    whiteList:

  rsa:
    privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
    publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'

  i18n:
    # 是否开启国际化
    enable: true
    # 后端加载的message
    # basename: classpath:message/messages-common,classpath:message/messages-sirmapp,classpath:message/messages-workflow,classpath:message/messages-org
    # 默认的message编码
    defaultEncoding: UTF-8
    # code找不到对应的message时,是否默认使用code返回
    useCodeAsDefaultMessage : true
    # 默认的语言环境
    defaultLocale : zh_CN
    # 系统支持的语言环境
    supportedLocaleInfoList:
      - locale: zh_CN
        localeName: 简体中文
        localeChineseName: 简体中文
      - locale: zh_HK
        localeName: 繁體中文
        localeChineseName: 繁体中文
    # 语言环境切换时统一使用的headerName
    headerName: lang

  attachment:
    #附件存放地址
    store-home: /home/<USER>/attachstore/
    #附件存放方式 : db、file、fastDFS、minio
    store-type: db

  tempdir:
    #临时目录
    path: /home/<USER>/temp/

  business-log:
    # 日志路径
    log-file-root-path: /home/<USER>/logs/

# knife4j相关的配置
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    username: admin
    password: sinitek-swagger

## 全局加解密、验签名
sirm:
  encrypt:
    encryptStrategy: ALL
    signDebug: true
    debug: true
    responseEncryptUriIgnoreList: /frontend/api/login,/frontend/api/login-init,/frontend/api/ue/exec,/frontend/api/captcha,/frontend/api/security/public-key,/frontend/api/properties,doc.html,/sinicube/check/**
    requestDecyptUriIgnoreList: /frontend/api/login,/frontend/api/login-init,/frontend/api/ue/exec,/frontend/api/captcha,/frontend/api/security/public-key,/frontend/api/properties,doc.html,/sinicube/check/**
    ignoreSuffix: .html,.json