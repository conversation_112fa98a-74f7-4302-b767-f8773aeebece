<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sinitek-sinicube</artifactId>
        <version>8.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sinitek-license</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>

        <!-- 达梦数据库依赖 -->
        <dependency>
            <groupId>com.dm8</groupId>
            <artifactId>DmJdbcDriver</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 南大通用数据库依赖 -->
        <dependency>
            <groupId>com.gbase</groupId>
            <artifactId>ifxjdbc</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 海量数据库, 默认注释 -->
        <dependency>
            <groupId>com.vastdata</groupId>
            <artifactId>VastbaseG100</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 附件存储支持切到minio,不需要该功能可删除此依赖 -->
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-attachment-by-minio</artifactId>
            <version>${sinicube.version}</version>
        </dependency>

        <!-- 业务日志支持切到es,不需要该功能可删除此依赖-->
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-business-log-by-es</artifactId>
        </dependency>

        <!-- 消息模版支持企业微信 -->
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-wxwork</artifactId>
        </dependency>

        <!-- 多因子登录验证插件 -->
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-multifactor-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sirmapp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 注册中心默认选择 Nacos, 如果使用eureka可自行切换-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--        </dependency>-->
    </dependencies>

    <dependencyManagement>
<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>org.springframework.ai</groupId>-->
<!--                <artifactId>spring-ai-bom</artifactId>-->
<!--                <version>${spring-ai.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--        </dependencies>-->
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <!-- 使打包后的jar不包含assembly目录 -->
                        <exclude>assembly/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/resources/assembly/default.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!--MAVEN打包选择运行环境-->
    <!--
         1:dev(默认) 开发环境
         2:test 测试环境
         3:prod 生产环境
    -->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>
